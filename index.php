<?php
/**
 * Pagina de căutare în masă pentru portalul judiciar
 * Permite căutarea simultană pentru multiple criterii
 */

// CRITICAL FIX: Verificăm IMEDIAT dacă este o cerere de export
// TREBUIE să fie PRIMUL lucru pentru a evita contaminarea cu HTML
if (isset($_GET['export']) && isset($_GET['bulk_results']) && $_GET['bulk_results'] === '1') {
    // Includere DOAR fișierele necesare pentru export (fără header HTML)
    require_once 'includes/config.php';
    require_once 'includes/functions.php';
    require_once 'services/DosarService.php';

    // Procesăm exportul în funcție de tip și ieșim IMEDIAT
    if ($_GET['export'] === 'xlsx') {
        handleBulkExcelExportOnly();
    } else {
        handleBulkCsvExportOnly();
    }
    exit; // STOP - nu mai executăm nimic altceva
}

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Get the institutions list for proper court name mapping
global $institutii;
$institutii = getInstanteList();

// Inițializare variabile
$bulkSearchTerms = '';
$results = [];
$searchResults = [];
$totalResults = 0;
$hasSearchCriteria = false;
$error = null;


// Procesare parametri de intrare
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $bulkSearchTerms = isset($_POST['bulkSearchTerms']) ? trim($_POST['bulkSearchTerms']) : '';

    // Procesare filtre avansate
    $advancedFilters = [
        'institutie' => isset($_POST['institutie']) && $_POST['institutie'] !== '' ? $_POST['institutie'] : null,
        'categorieInstanta' => isset($_POST['categorieInstanta']) ? trim($_POST['categorieInstanta']) : '',
        'categorieCaz' => isset($_POST['categorieCaz']) ? trim($_POST['categorieCaz']) : '',
        'dataInceput' => isset($_POST['dataInceput']) ? trim($_POST['dataInceput']) : '',
        'dataSfarsit' => isset($_POST['dataSfarsit']) ? trim($_POST['dataSfarsit']) : ''
    ];

    // Check if we have any search criteria (either search terms OR advanced filters)
    $hasAdvancedFilters = !empty($advancedFilters['institutie']) ||
                         !empty($advancedFilters['categorieInstanta']) ||
                         !empty($advancedFilters['categorieCaz']) ||
                         !empty($advancedFilters['dataInceput']) ||
                         !empty($advancedFilters['dataSfarsit']);

    if (!empty($bulkSearchTerms) || $hasAdvancedFilters) {
        $hasSearchCriteria = true;

        // Validare date
        $dateRangeValidation = validateDateRange($advancedFilters['dataInceput'], $advancedFilters['dataSfarsit']);
        if (!$dateRangeValidation['valid']) {
            $error = $dateRangeValidation['error'];
        } else {
            // Handle search terms if provided
            if (!empty($bulkSearchTerms)) {
                // Procesare termeni de căutare cu detectare automată de tip
                $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);

                // Validare număr de termeni
                if (count($searchTermsData) > 100) {
                    $error = 'Numărul maxim de termeni de căutare este 100. Ați introdus ' . count($searchTermsData) . ' termeni.';
                } elseif (count($searchTermsData) === 0) {
                    $error = 'Nu au fost găsiți termeni valizi de căutare.';
                } else {
            // Efectuare căutare în masă cu detectare automată și filtre avansate
            try {
                $searchResults = performBulkSearchWithFilters($searchTermsData, $advancedFilters);

                // Calculare total rezultate
                $totalResults = array_sum(array_map(function($result) {
                    return count($result['results']);
                }, $searchResults));

                // Verificăm dacă s-a folosit fallback pentru instituție
                $institutionFallbackUsed = false;
                foreach ($searchResults as $result) {
                    if (!empty($result['institution_fallback'])) {
                        $institutionFallbackUsed = true;
                        break;
                    }
                }

                // Adăugăm notificare pentru fallback
                if ($institutionFallbackUsed && !empty($advancedFilters['institutie'])) {
                    $institutii = getInstanteList();
                    $institutionName = $institutii[$advancedFilters['institutie']] ?? $advancedFilters['institutie'];
                    $error = "Notă: Codul instituției '{$institutionName}' nu este recunoscut de API-ul SOAP. Căutarea a fost efectuată fără filtrul de instituție și rezultatele au fost filtrate local.";
                }

            } catch (Exception $e) {
                $error = 'Eroare la căutarea în masă: ' . $e->getMessage();
                error_log('Eroare bulk search: ' . $e->getMessage());
            }
                }
            } else {
                // Filter-only search (no search terms provided)
                try {
                    // Create a dummy search term for filter-only searches
                    $searchTermsData = [['term' => '', 'type' => 'filter_only']];

                    // Perform search with only filters
                    $searchResults = performBulkSearchWithFilters($searchTermsData, $advancedFilters);

                    // Calculate total results
                    $totalResults = 0;
                    foreach ($searchResults as $result) {
                        $totalResults += $result['count'];
                    }

                    // Add notification about filter-only search
                    if ($totalResults > 0) {
                        $notifications[] = [
                            'type' => 'info',
                            'message' => "Căutare efectuată folosind doar filtrele avansate. Au fost găsite {$totalResults} rezultate."
                        ];
                    }

                } catch (Exception $e) {
                    $error = 'Eroare la căutarea cu filtre: ' . $e->getMessage();
                    error_log('Eroare filter-only search: ' . $e->getMessage());
                }
            }
        }
    }
}

// Handle URL parameters for party name search redirection from detalii_dosar.php
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['numeParte']) && !empty($_GET['numeParte'])) {
    $partyName = trim($_GET['numeParte']);
    $autoSubmit = isset($_GET['autoSubmit']) && $_GET['autoSubmit'] === 'true';

    if (!empty($partyName)) {
        // Set the search terms for the form
        $bulkSearchTerms = $partyName;
        $hasSearchCriteria = true;

        // If autoSubmit is true, perform the search automatically
        if ($autoSubmit) {
            // Process the search as if it was a POST request
            $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);

            if (count($searchTermsData) > 0) {
                try {
                    // Perform bulk search with no advanced filters
                    $searchResults = performBulkSearch($searchTermsData);

                    // Calculate total results
                    $totalResults = array_sum(array_map(function($result) {
                        return count($result['results']);
                    }, $searchResults));

                } catch (Exception $e) {
                    $error = 'Eroare la căutarea automată: ' . $e->getMessage();
                    error_log('Eroare auto search: ' . $e->getMessage());
                }
            }
        }
    }
}

// Functions are working correctly - test confirmed

/**
 * Detectează automat tipul de căutare pe baza conținutului termenului
 * Returnează 'numarDosar', 'obiectDosar', sau 'numeParte'
 */
function detectSearchType($term) {
    // Eliminăm ghilimelele pentru analiză
    $cleanTerm = trim($term, '"\'');

    // Pattern pentru numărul de dosar (ex: 1234/2023, 12345/118/2023, etc.)
    if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
        return 'numarDosar';
    }

    // Pattern pentru numărul de dosar cu prefixe (ex: nr. 1234/2023, dosar 12345/118/2023)
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
        return 'numarDosar';
    }

    // Verificăm dacă pare să fie un obiect de dosar (conține cuvinte cheie specifice)
    $obiectKeywords = [
        'divorț', 'divort', 'executare', 'executare silită', 'executare silita',
        'faliment', 'insolvență', 'insolvenţă', 'insolventa', 'reorganizare',
        'contencios', 'contencios administrativ', 'fiscal', 'penal',
        'contravenție', 'contraventie', 'contravention', 'civil',
        'comercial', 'muncă', 'munca', 'familie', 'succesiune',
        'proprietate', 'proprietate intelectuală', 'proprietate intelectuala',
        'daune', 'prejudicii', 'contract', 'contractual', 'delict',
        'răspundere', 'raspundere', 'asigurare', 'asigurări', 'asigurari'
    ];

    $lowerTerm = mb_strtolower($cleanTerm, 'UTF-8');
    foreach ($obiectKeywords as $keyword) {
        if (mb_strpos($lowerTerm, mb_strtolower($keyword, 'UTF-8'), 0, 'UTF-8') !== false) {
            return 'obiectDosar';
        }
    }

    // Verificăm dacă pare să fie numele unei companii (conține SC, SRL, SA, etc.)
    if (preg_match('/\b(?:SC|SRL|SA|SNC|SCS|PFA|II|IF|ONG|ASOCIAȚIA|ASOCIATIA|FUNDAȚIA|FUNDATIA)\b/i', $cleanTerm)) {
        return 'numeParte';
    }

    // Verificăm dacă pare să fie un nume de persoană (conține cel puțin 2 cuvinte cu prima literă mare)
    $words = explode(' ', $cleanTerm);
    $capitalizedWords = 0;
    foreach ($words as $word) {
        if (preg_match('/^[A-ZĂÂÎȘȚ]/u', $word)) {
            $capitalizedWords++;
        }
    }

    // Dacă avem cel puțin 2 cuvinte cu prima literă mare, probabil e nume de persoană
    if (count($words) >= 2 && $capitalizedWords >= 2) {
        return 'numeParte';
    }

    // Default: nume parte (cel mai comun tip de căutare)
    return 'numeParte';
}

/**
 * Parsează termenii de căutare din textarea și detectează automat tipurile
 * Suportă atât separare prin virgulă, cât și prin linie nouă
 * Returnează array cu termenii și tipurile lor detectate
 */
function parseBulkSearchTerms($input) {
    // Înlocuim virgulele cu linii noi pentru procesare uniformă
    $input = str_replace(',', "\n", $input);

    // Împărțim pe linii și curățăm
    $terms = explode("\n", $input);
    $cleanTerms = [];

    foreach ($terms as $term) {
        $term = trim($term);
        if (!empty($term) && strlen($term) >= 2) { // Minim 2 caractere
            $cleanTerms[] = [
                'term' => $term,
                'type' => detectSearchType($term)
            ];
        }
    }

    // Eliminăm duplicatele pe baza termenului
    $uniqueTerms = [];
    $seenTerms = [];

    foreach ($cleanTerms as $termData) {
        $termKey = strtolower($termData['term']);
        if (!in_array($termKey, $seenTerms)) {
            $uniqueTerms[] = $termData;
            $seenTerms[] = $termKey;
        }
    }

    return $uniqueTerms;
}

/**
 * Efectuează căutarea în masă pentru toți termenii cu detectare automată de tip
 */
function performBulkSearch($searchTermsData) {
    return performBulkSearchWithFilters($searchTermsData, []);
}

/**
 * Validează data în format românesc DD.MM.YYYY
 */
function validateRomanianDate($dateString) {
    if (empty($dateString)) {
        return ['valid' => true, 'date' => '', 'error' => ''];
    }

    // Verificăm formatul DD.MM.YYYY
    if (!preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $dateString, $matches)) {
        return ['valid' => false, 'date' => '', 'error' => 'Formatul datei trebuie să fie ZZ.LL.AAAA (ex: 15.03.2023)'];
    }

    $day = (int)$matches[1];
    $month = (int)$matches[2];
    $year = (int)$matches[3];

    // Verificăm validitatea datei
    if (!checkdate($month, $day, $year)) {
        return ['valid' => false, 'date' => '', 'error' => 'Data introdusă nu este validă'];
    }

    // Verificăm limitele anului (nu acceptăm date prea vechi sau prea în viitor)
    $currentYear = (int)date('Y');
    if ($year < 1990 || $year > $currentYear + 5) {
        return ['valid' => false, 'date' => '', 'error' => "Anul trebuie să fie între 1990 și " . ($currentYear + 5)];
    }

    // Convertim la format YYYY-MM-DD pentru SOAP API
    $formattedDate = sprintf('%04d-%02d-%02d', $year, $month, $day);

    return ['valid' => true, 'date' => $formattedDate, 'error' => ''];
}

/**
 * Validează intervalul de date (data început <= data sfârșit)
 */
function validateDateRange($startDate, $endDate) {
    if (empty($startDate) || empty($endDate)) {
        return ['valid' => true, 'error' => ''];
    }

    $startValidation = validateRomanianDate($startDate);
    $endValidation = validateRomanianDate($endDate);

    if (!$startValidation['valid']) {
        return ['valid' => false, 'error' => 'Data început: ' . $startValidation['error']];
    }

    if (!$endValidation['valid']) {
        return ['valid' => false, 'error' => 'Data sfârșit: ' . $endValidation['error']];
    }

    // Verificăm că data sfârșit este după data început
    if ($startValidation['date'] > $endValidation['date']) {
        return ['valid' => false, 'error' => 'Data sfârșit trebuie să fie după data început'];
    }

    return ['valid' => true, 'error' => ''];
}

/**
 * Validează și mapează codul instituției pentru SOAP API
 */
function validateAndMapInstitutionCode($institutionCode) {
    if (empty($institutionCode)) {
        return null;
    }

    // Mapare pentru codurile de instituții care pot fi diferite în SOAP API
    // Bazat pe testarea SOAP API și documentația disponibilă
    $institutionMapping = [
        // Înalta Curte - poate să nu fie suportată direct în SOAP API
        'InaltaCurtedeCASSATIESIJUSTITIE' => null,

        // Curți de Apel - testăm cu codurile exacte
        'CurteadeApelBUCURESTI' => 'CurteadeApelBUCURESTI',
        'CurteadeApelCLUJ' => 'CurteadeApelCLUJ',
        'CurteadeApelTIMISOARA' => 'CurteadeApelTIMISOARA',
        'CurteadeApelCONSTANTA' => 'CurteadeApelCONSTANTA',
        'CurteadeApelCRAIOVA' => 'CurteadeApelCRAIOVA',
        'CurteadeApelIASI' => 'CurteadeApelIASI',

        // Tribunale - pot avea variații în denumire
        'TribunalulBUCURESTI' => 'TribunalulBUCURESTI',
        'TribunalulCLUJ' => 'TribunalulCLUJ',
        'TribunalulTIMIS' => 'TribunalulTIMIS',
        'TribunalulCONSTANTA' => 'TribunalulCONSTANTA',
        'TribunalulBRASSOV' => 'TribunalulBRASSOV',
        'TribunalulIASI' => 'TribunalulIASI',

        // Judecătorii București - pot avea coduri specifice
        'JudecatoriaSECTORUL1BUCURESTI' => 'JudecatoriaSECTORUL1BUCURESTI',
        'JudecatoriaSECTORUL2BUCURESTI' => 'JudecatoriaSECTORUL2BUCURESTI',
        'JudecatoriaSECTORUL3BUCURESTI' => 'JudecatoriaSECTORUL3BUCURESTI',
        'JudecatoriaSECTORUL4BUCURESTI' => 'JudecatoriaSECTORUL4BUCURESTI',
        'JudecatoriaSECTORUL5BUCURESTI' => 'JudecatoriaSECTORUL5BUCURESTI',
        'JudecatoriaSECTORUL6BUCURESTI' => 'JudecatoriaSECTORUL6BUCURESTI',

        // Alte judecătorii importante
        'JudecatoriaCLUJNAPOCA' => 'JudecatoriaCLUJNAPOCA',
        'JudecatoriaTIMISOARA' => 'JudecatoriaTIMISOARA',
        'JudecatoriaCONSTANTA' => 'JudecatoriaCONSTANTA',
        'JudecatoriaIASI' => 'JudecatoriaIASI',
        'JudecatoriaBRASSOV' => 'JudecatoriaBRASSOV',

        // Coduri alternative care pot apărea în SOAP API
        'TBBU' => 'TribunalulBUCURESTI', // Cod scurt pentru Tribunalul București
        'CAB' => 'CurteadeApelBUCURESTI', // Cod scurt pentru Curtea de Apel București
        'ICCJ' => null, // Înalta Curte - cod scurt

        // SOAP API incompatible codes - map to working alternatives
        'TribunalulBACU' => 'CurteadeApelBACU', // Tribunal Bacău not recognized by SOAP, use Appeals Court
        'TribunalulSUCEAVA' => 'CurteadeApelSUCEAVA', // Map to appeals court for SOAP compatibility
        'JudecatoriaROMANI' => 'JudecatoriaROMAN', // Correct typo: ROMANI -> ROMAN
    ];

    // Verificăm dacă avem o mapare specifică
    if (array_key_exists($institutionCode, $institutionMapping)) {
        return $institutionMapping[$institutionCode];
    }

    // Dacă nu avem mapare specifică, returnăm codul original
    return $institutionCode;
}

/**
 * Efectuează căutarea în masă pentru toți termenii cu detectare automată de tip și filtre avansate
 */
function performBulkSearchWithFilters($searchTermsData, $advancedFilters = []) {
    $dosarService = new DosarService();
    $results = [];

    // Validăm și mapăm codul instituției
    $mappedInstitutionCode = validateAndMapInstitutionCode($advancedFilters['institutie'] ?? null);

    foreach ($searchTermsData as $termData) {
        $term = $termData['term'];
        $searchType = $termData['type'];

        try {
            // Procesăm datele pentru SOAP API
            $dataStart = '';
            $dataStop = '';

            if (!empty($advancedFilters['dataInceput'])) {
                $startValidation = validateRomanianDate($advancedFilters['dataInceput']);
                if ($startValidation['valid']) {
                    $dataStart = $startValidation['date'] . 'T00:00:00';
                }
            }

            if (!empty($advancedFilters['dataSfarsit'])) {
                $endValidation = validateRomanianDate($advancedFilters['dataSfarsit']);
                if ($endValidation['valid']) {
                    $dataStop = $endValidation['date'] . 'T23:59:59';
                }
            }

            // Construim parametrii de căutare
            $searchParams = [
                'numarDosar' => '',
                'institutie' => $mappedInstitutionCode,
                'numeParte' => '',
                'obiectDosar' => '',
                'dataStart' => $dataStart,
                'dataStop' => $dataStop,
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                'categorieInstanta' => $advancedFilters['categorieInstanta'] ?? '',
                'categorieCaz' => $advancedFilters['categorieCaz'] ?? ''
            ];

            // Setăm parametrul corespunzător tipului de căutare detectat automat
            switch ($searchType) {
                case 'numarDosar':
                    $searchParams['numarDosar'] = $term;
                    break;
                case 'obiectDosar':
                    $searchParams['obiectDosar'] = $term;
                    break;
                case 'filter_only':
                    // For filter-only searches, don't set any search term parameters
                    // The search will be based purely on filters (institution, dates, etc.)
                    break;
                case 'numeParte':
                default:
                    $searchParams['numeParte'] = $term;
                    break;
            }

            // Efectuăm căutarea cu gestionarea erorilor de instituție
            $institutionFallback = false;
            try {
                $termResults = $dosarService->cautareAvansata($searchParams);
            } catch (Exception $soapException) {
                // Dacă eroarea este legată de instituție, încercăm fără filtrul de instituție
                if (strpos($soapException->getMessage(), 'not valid') !== false ||
                    strpos($soapException->getMessage(), 'invalid') !== false) {

                    error_log("Institution code '{$mappedInstitutionCode}' not valid for SOAP API, retrying without institution filter");

                    // Reîncercăm fără filtrul de instituție
                    $searchParams['institutie'] = null;
                    $termResults = $dosarService->cautareAvansata($searchParams);
                    $institutionFallback = true;

                    // Aplicăm filtrarea client-side pentru instituție dacă avem rezultate
                    if (!empty($termResults) && !empty($advancedFilters['institutie'])) {
                        $termResults = filterResultsByInstitution($termResults, $advancedFilters['institutie']);
                    }

                    // Aplicăm filtrarea client-side pentru categoria cazului dacă avem rezultate
                    if (!empty($termResults) && !empty($advancedFilters['categorieCaz'])) {
                        $termResults = filterResultsByCaseCategory($termResults, $advancedFilters['categorieCaz']);
                    }
                } else {
                    throw $soapException;
                }
            }

            // IMPORTANT: Apply client-side case category filtering to ALL results, not just fallback
            // This ensures case category filtering works even when SOAP API doesn't support it natively
            if (!empty($termResults) && !empty($advancedFilters['categorieCaz'])) {
                error_log("MAIN SEARCH: Applying case category filter '{$advancedFilters['categorieCaz']}' to " . count($termResults) . " results");
                $originalCount = count($termResults);
                $termResults = filterResultsByCaseCategory($termResults, $advancedFilters['categorieCaz']);
                $filteredCount = count($termResults);
                error_log("MAIN SEARCH: Case category filtering reduced results from $originalCount to $filteredCount");
            }

            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => $termResults ?: [],
                'count' => count($termResults ?: []),
                'error' => null,
                'filters' => $advancedFilters,
                'institution_fallback' => $institutionFallback
            ];

        } catch (Exception $e) {
            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => [],
                'count' => 0,
                'error' => $e->getMessage(),
                'filters' => $advancedFilters,
                'institution_fallback' => false
            ];
        }
    }

    return $results;
}

/**
 * Filtrează rezultatele după categoria cazului (client-side)
 */
function filterResultsByCaseCategory($termResults, $caseCategory) {
    if (empty($caseCategory) || empty($termResults)) {
        return $termResults;
    }

    // Log for debugging
    error_log("CASE CATEGORY FILTER: Filtering by category '$caseCategory'");
    error_log("CASE CATEGORY FILTER: Input has " . count($termResults) . " results");

    $filteredResults = [];

    foreach ($termResults as $dosar) {
        // Verificăm categoria cazului
        $dosarCategory = strtolower($dosar->categorieCaz ?? '');
        $filterCategory = strtolower($caseCategory);

        // Log the case category for debugging
        error_log("CASE CATEGORY FILTER: Checking dosar {$dosar->numar} with category '$dosarCategory' against filter '$filterCategory'");

        // Enhanced mappings for real-world judicial categories
        $categoryMappings = [
            'civil' => [
                'civil', 'civile', 'civila', 'civile', 'civilă',
                'civillitigii', 'civillitigiicu', 'civillitigiicuprofesionistii'
            ],
            'penal' => [
                'penal', 'penale', 'penala', 'penală',
                'penallitigii', 'penallitigiicu', 'penallitigiicuprofesionistii',
                'penal litigii', 'penal litigii cu profesionistii'
            ],
            'comercial' => [
                'comercial', 'comerciale', 'comerciala', 'comercială', 'comert', 'comerț',
                'comerciallitigii', 'comerciallitigiicu', 'comerciallitigiicuprofesionistii',
                'comercial litigii', 'comercial litigii cu profesionistii'
            ],
            'contencios_administrativ' => [
                'contencios', 'administrativ', 'contencios administrativ', 'contencioasa', 'contencioasă',
                'contencios-administrativ', 'contencios_administrativ'
            ],
            'fiscal' => [
                'fiscal', 'fiscale', 'fiscala', 'fiscală', 'taxe', 'taxa', 'taxă',
                'fiscallitigii', 'fiscallitigiicu', 'fiscallitigiicuprofesionistii'
            ],
            'munca' => [
                'munca', 'muncă', 'asigurari', 'asigurări sociale', 'asigurari sociale',
                'muncalitigii', 'muncalitigiicu', 'muncalitigiicuprofesionistii',
                'munca litigii', 'munca litigii cu profesionistii'
            ],
            'familie' => [
                'familie', 'minori', 'familie și minori', 'familie si minori',
                'familielitigii', 'familielitigiicu', 'familielitigiicuprofesionistii'
            ],
            'executare' => [
                'executare', 'executari', 'executoriat', 'executări',
                'executarelitigii', 'executarelitigiicu', 'executarelitigiicuprofesionistii'
            ],
            'insolventa' => [
                'insolventa', 'insolvență', 'faliment', 'insolvabilitate',
                'insolventalitigii', 'insolventalitigiicu', 'insolventalitigiicuprofesionistii'
            ]
        ];

        $shouldInclude = false;

        // Verificăm dacă categoria dosarului se potrivește cu filtrul
        if (isset($categoryMappings[$filterCategory])) {
            foreach ($categoryMappings[$filterCategory] as $mapping) {
                if (stripos($dosarCategory, $mapping) !== false) {
                    $shouldInclude = true;
                    error_log("CASE CATEGORY FILTER: Match found for '$dosarCategory' with mapping '$mapping'");
                    break;
                }
            }
        } else {
            // Căutare directă dacă nu avem mapare
            if (stripos($dosarCategory, $filterCategory) !== false) {
                $shouldInclude = true;
                error_log("CASE CATEGORY FILTER: Direct match found for '$dosarCategory' with filter '$filterCategory'");
            }
        }

        if ($shouldInclude) {
            $filteredResults[] = $dosar;
            error_log("CASE CATEGORY FILTER: Including dosar {$dosar->numar}");
        } else {
            error_log("CASE CATEGORY FILTER: Excluding dosar {$dosar->numar}");
        }
    }

    error_log("CASE CATEGORY FILTER: Filtered results count: " . count($filteredResults));
    return $filteredResults;
}

/**
 * Filtrează rezultatele după instituție (client-side)
 */
function filterResultsByInstitution($results, $institutionCode) {
    if (empty($institutionCode) || empty($results)) {
        return $results;
    }

    $institutii = getInstanteList();
    $institutionName = $institutii[$institutionCode] ?? '';

    return array_filter($results, function($result) use ($institutionCode, $institutionName) {
        $resultInstitution = $result->institutie ?? '';

        // Verificăm match exact cu codul
        if ($resultInstitution === $institutionCode) {
            return true;
        }

        // Verificăm match cu numele instituției
        if (!empty($institutionName) && stripos($resultInstitution, $institutionName) !== false) {
            return true;
        }

        // Verificăm match parțial cu codul
        if (stripos($resultInstitution, $institutionCode) !== false) {
            return true;
        }

        return false;
    });
}

/**
 * DEDICATED CSV EXPORT FUNCTION FOR BULK SEARCH - NO HTML CONTAMINATION
 */
function handleBulkCsvExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Reconstituim căutarea din parametrii GET
        $bulkSearchTerms = isset($_GET['bulkSearchTerms']) ? trim($_GET['bulkSearchTerms']) : '';
        $exactMatchOnly = isset($_GET['exactMatchOnly']) && $_GET['exactMatchOnly'] === 'true';

        // Reconstituim filtrele avansate din parametrii GET
        $advancedFilters = [
            'institutie' => isset($_GET['institutie']) && $_GET['institutie'] !== '' ? $_GET['institutie'] : null,
            'categorieInstanta' => isset($_GET['categorieInstanta']) ? trim($_GET['categorieInstanta']) : '',
            'categorieCaz' => isset($_GET['categorieCaz']) ? trim($_GET['categorieCaz']) : '',
            'dataInceput' => isset($_GET['dataInceput']) ? trim($_GET['dataInceput']) : '',
            'dataSfarsit' => isset($_GET['dataSfarsit']) ? trim($_GET['dataSfarsit']) : ''
        ];

        // Validăm și mapăm codul instituției pentru export
        if (!empty($advancedFilters['institutie'])) {
            $advancedFilters['institutie'] = validateAndMapInstitutionCode($advancedFilters['institutie']);
        }

        // Check if we have either search terms OR advanced filters
        $hasAdvancedFilters = !empty($advancedFilters['institutie']) ||
                             !empty($advancedFilters['categorieInstanta']) ||
                             !empty($advancedFilters['categorieCaz']) ||
                             !empty($advancedFilters['dataInceput']) ||
                             !empty($advancedFilters['dataSfarsit']);

        if (empty($bulkSearchTerms) && !$hasAdvancedFilters) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => 'Nu există termeni de căutare sau filtre pentru export'], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Procesare termeni cu detectare automată și efectuare căutare cu filtre
        if (!empty($bulkSearchTerms)) {
            $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);
        } else {
            // Filter-only search: create dummy search term
            $searchTermsData = [['term' => '', 'type' => 'filter_only']];
        }
        $searchResults = performBulkSearchWithFilters($searchTermsData, $advancedFilters);

        // Colectare toate rezultatele pentru export
        $allResults = [];
        foreach ($searchResults as $termResult) {
            foreach ($termResult['results'] as $dosar) {
                $dosar->searchTerm = $termResult['term']; // Adăugăm termenul de căutare
                $dosar->searchType = $termResult['type']; // Adăugăm tipul de căutare

                // Apply enhanced exact match filter if requested
                if ($exactMatchOnly) {
                    $shouldInclude = false;

                    // Always include case number searches (they are inherently exact)
                    if ($termResult['type'] === 'numarDosar') {
                        $shouldInclude = true;
                        error_log("CSV EXPORT: Including case number search result: {$dosar->numar}");
                    }
                    // Include case object searches as well
                    else if ($termResult['type'] === 'obiectDosar') {
                        $shouldInclude = true;
                        error_log("CSV EXPORT: Including case object search result: {$dosar->numar}");
                    }
                    // For party name searches, check for exact matches
                    else if ($termResult['type'] === 'numeParte') {
                        $parti = $dosar->parti ?? [];
                        if (!is_array($parti)) {
                            $parti = [];
                        }

                        $relevantParty = getRelevantPartyName($parti, $termResult['term'], $termResult['type']);
                        if (!empty($relevantParty)) {
                            $matchType = getMatchType($relevantParty, $termResult['term'], false);
                            // Check for exact phrase match if search term has quotes
                            if (preg_match('/^"(.+)"$/', trim($termResult['term']), $matches)) {
                                $cleanTerm = $matches[1];
                                $matchType = getMatchType($relevantParty, $cleanTerm, true);
                            }

                            // Debug logging for Becali case
                            $isBecaliCase = (stripos($termResult['term'], 'becali') !== false || stripos($relevantParty, 'becali') !== false);
                            if ($isBecaliCase) {
                                error_log("CSV EXPORT DEBUG: Term='{$termResult['term']}', Party='$relevantParty', MatchType='$matchType'");
                            }

                            // Only include if exact match, exact phrase match, partial phrase match, or partial match
                            // This matches the client-side filtering logic that includes .partial-match elements
                            if ($matchType === 'exact' || $matchType === 'exact-phrase' || $matchType === 'partial-phrase' || $matchType === 'partial') {
                                $shouldInclude = true;
                                if ($isBecaliCase) {
                                    error_log("CSV EXPORT DEBUG: Including dosar {$dosar->numar} with match type '$matchType'");
                                }
                            } else {
                                if ($isBecaliCase) {
                                    error_log("CSV EXPORT DEBUG: Excluding dosar {$dosar->numar} with match type '$matchType'");
                                }
                            }
                        }
                    }

                    if ($shouldInclude) {
                        $allResults[] = $dosar;
                    }
                } else {
                    $allResults[] = $dosar;
                }
            }
        }

        if (empty($allResults)) {
            http_response_code(404);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => 'Nu există rezultate pentru export'], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Generăm numele fișierului cu indicarea filtrării
        if ($exactMatchOnly) {
            $filename = 'Rezultate_Filtrate_Exacte_si_Numere_Dosar_' . date('Y-m-d_H-i-s') . '.csv';
        } else {
            $filename = 'Rezultate_Cautare_Masa_' . date('Y-m-d_H-i-s') . '.csv';
        }

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Generăm fișierul CSV
        generateBulkCsvFile($allResults, $filename);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea CSV: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * DEDICATED EXCEL EXPORT FUNCTION FOR BULK SEARCH - TRUE XLSX FORMAT
 */
function handleBulkExcelExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Reconstituim căutarea din parametrii GET
        $bulkSearchTerms = isset($_GET['bulkSearchTerms']) ? trim($_GET['bulkSearchTerms']) : '';
        $exactMatchOnly = isset($_GET['exactMatchOnly']) && $_GET['exactMatchOnly'] === 'true';

        // Reconstituim filtrele avansate din parametrii GET
        $advancedFilters = [
            'institutie' => isset($_GET['institutie']) && $_GET['institutie'] !== '' ? $_GET['institutie'] : null,
            'categorieInstanta' => isset($_GET['categorieInstanta']) ? trim($_GET['categorieInstanta']) : '',
            'categorieCaz' => isset($_GET['categorieCaz']) ? trim($_GET['categorieCaz']) : '',
            'dataInceput' => isset($_GET['dataInceput']) ? trim($_GET['dataInceput']) : '',
            'dataSfarsit' => isset($_GET['dataSfarsit']) ? trim($_GET['dataSfarsit']) : ''
        ];

        // Validăm și mapăm codul instituției pentru export
        if (!empty($advancedFilters['institutie'])) {
            $advancedFilters['institutie'] = validateAndMapInstitutionCode($advancedFilters['institutie']);
        }

        // Check if we have either search terms OR advanced filters
        $hasAdvancedFilters = !empty($advancedFilters['institutie']) ||
                             !empty($advancedFilters['categorieInstanta']) ||
                             !empty($advancedFilters['categorieCaz']) ||
                             !empty($advancedFilters['dataInceput']) ||
                             !empty($advancedFilters['dataSfarsit']);

        if (empty($bulkSearchTerms) && !$hasAdvancedFilters) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => 'Nu există termeni de căutare sau filtre pentru export'], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Procesare termeni cu detectare automată și efectuare căutare cu filtre
        if (!empty($bulkSearchTerms)) {
            $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);
        } else {
            // Filter-only search: create dummy search term
            $searchTermsData = [['term' => '', 'type' => 'filter_only']];
        }
        $searchResults = performBulkSearchWithFilters($searchTermsData, $advancedFilters);

        // Colectare toate rezultatele pentru export
        $allResults = [];
        foreach ($searchResults as $termResult) {
            foreach ($termResult['results'] as $dosar) {
                $dosar->searchTerm = $termResult['term']; // Adăugăm termenul de căutare
                $dosar->searchType = $termResult['type']; // Adăugăm tipul de căutare

                // Apply enhanced exact match filter if requested
                if ($exactMatchOnly) {
                    $shouldInclude = false;

                    // Always include case number searches (they are inherently exact)
                    if ($termResult['type'] === 'numarDosar') {
                        $shouldInclude = true;
                        error_log("EXCEL EXPORT: Including case number search result: {$dosar->numar}");
                    }
                    // Include case object searches as well
                    else if ($termResult['type'] === 'obiectDosar') {
                        $shouldInclude = true;
                        error_log("EXCEL EXPORT: Including case object search result: {$dosar->numar}");
                    }
                    // For party name searches, check for exact matches
                    else if ($termResult['type'] === 'numeParte') {
                        $parti = $dosar->parti ?? [];
                        if (!is_array($parti)) {
                            $parti = [];
                        }

                        $relevantParty = getRelevantPartyName($parti, $termResult['term'], $termResult['type']);
                        if (!empty($relevantParty)) {
                            $matchType = getMatchType($relevantParty, $termResult['term'], false);
                            // Check for exact phrase match if search term has quotes
                            if (preg_match('/^"(.+)"$/', trim($termResult['term']), $matches)) {
                                $cleanTerm = $matches[1];
                                $matchType = getMatchType($relevantParty, $cleanTerm, true);
                            }

                            // Debug logging for Becali case
                            $isBecaliCase = (stripos($termResult['term'], 'becali') !== false || stripos($relevantParty, 'becali') !== false);
                            if ($isBecaliCase) {
                                error_log("EXCEL EXPORT DEBUG: Term='{$termResult['term']}', Party='$relevantParty', MatchType='$matchType'");
                            }

                            // Only include if exact match, exact phrase match, partial phrase match, or partial match
                            // This matches the client-side filtering logic that includes .partial-match elements
                            if ($matchType === 'exact' || $matchType === 'exact-phrase' || $matchType === 'partial-phrase' || $matchType === 'partial') {
                                $shouldInclude = true;
                                if ($isBecaliCase) {
                                    error_log("EXCEL EXPORT DEBUG: Including dosar {$dosar->numar} with match type '$matchType'");
                                }
                            } else {
                                if ($isBecaliCase) {
                                    error_log("EXCEL EXPORT DEBUG: Excluding dosar {$dosar->numar} with match type '$matchType'");
                                }
                            }
                        }
                    }

                    if ($shouldInclude) {
                        $allResults[] = $dosar;
                    }
                } else {
                    $allResults[] = $dosar;
                }
            }
        }

        if (empty($allResults)) {
            http_response_code(404);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => 'Nu există rezultate pentru export'], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Generăm numele fișierului cu indicarea filtrării
        if ($exactMatchOnly) {
            $filename = 'Rezultate_Filtrate_Exacte_si_Numere_Dosar_' . date('Y-m-d_H-i-s') . '.xlsx';
        } else {
            $filename = 'Rezultate_Cautare_Masa_' . date('Y-m-d_H-i-s') . '.xlsx';
        }

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Generăm fișierul Excel
        generateBulkExcelFile($allResults, $filename);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea Excel: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * Generează fișier CSV pentru căutarea în masă
 */
function generateBulkCsvFile($results, $filename) {
    // Get institutions list for proper court name mapping
    $institutii = getInstanteList();

    // Setăm header-ele pentru descărcare CSV
    header('Content-Type: text/csv; charset=UTF-8');
    header("Content-Disposition: attachment; filename=\"{$filename}\"");
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    // Adăugăm BOM pentru UTF-8
    echo "\xEF\xBB\xBF";

    // Deschidem output stream
    $output = fopen('php://output', 'w');

    // Header CSV cu coloana pentru termenul de căutare și tipul detectat
    $headers = [
        'Termen Căutare',
        'Tip Detectat',
        'Număr Dosar',
        'Instanță',
        'Obiect',
        'Stadiu Procesual',
        'Data',
        'Ultima Modificare',
        'Categorie Caz',
        'Nume Parte',
        'Calitate'
    ];

    fputcsv($output, $headers, ';', '"');

    // Adăugăm datele
    foreach ($results as $dosar) {
        $searchTerm = $dosar->searchTerm ?? '';
        $searchType = $dosar->searchType ?? '';

        // FIXED: Ensure parti is an array for the functions
        $parti = $dosar->parti ?? [];
        if (!is_array($parti)) {
            $parti = [];
        }

        // Get proper institution name using the mapping
        $institutie = $dosar->institutie ?? '';
        $instituteName = !empty($institutie) ? ($institutii[$institutie] ?? $institutie) : '';

        // Convert search type to Romanian
        $searchTypeRo = '';
        switch ($searchType) {
            case 'numeParte':
                $searchTypeRo = 'Nume parte';
                break;
            case 'numarDosar':
                $searchTypeRo = 'Număr dosar';
                break;
            case 'obiectDosar':
                $searchTypeRo = 'Obiect dosar';
                break;
            default:
                $searchTypeRo = 'Necunoscut';
        }

        $row = [
            $searchTerm,
            $searchTypeRo,
            $dosar->numar ?? '',
            $instituteName,
            $dosar->obiect ?? '',
            $dosar->stadiuProcesual ?? '',
            $dosar->data ?? '',
            $dosar->dataModificare ?? '',
            $dosar->categorieCaz ?? '',
            getRelevantPartyName($parti, $searchTerm, $searchType) ?: '',
            getRelevantPartyQuality($parti, $searchTerm, $searchType) ?: ''
        ];

        fputcsv($output, $row, ';', '"');
    }

    fclose($output);
}

/**
 * Generează fișier Excel pentru căutarea în masă
 */
function generateBulkExcelFile($results, $filename) {
    // Get institutions list for proper court name mapping
    $institutii = getInstanteList();

    // Încărcăm PhpSpreadsheet
    require_once __DIR__ . '/vendor/autoload.php';

    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();

    // Setăm titlul foii
    $sheet->setTitle('Rezultate Căutare Masă');

    // Header-ele cu coloana pentru termenul de căutare și tipul detectat
    $headers = [
        'A1' => 'Termen Căutare',
        'B1' => 'Tip Detectat',
        'C1' => 'Număr Dosar',
        'D1' => 'Instanță',
        'E1' => 'Obiect',
        'F1' => 'Stadiu Procesual',
        'G1' => 'Data',
        'H1' => 'Ultima Modificare',
        'I1' => 'Categorie Caz',
        'J1' => 'Nume Parte',
        'K1' => 'Calitate'
    ];

    // Setăm header-ele
    foreach ($headers as $cell => $value) {
        $sheet->setCellValue($cell, $value);
    }

    // Stilizăm header-ul
    $headerStyle = [
        'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
        'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID, 'startColor' => ['rgb' => '007BFF']]
    ];
    $sheet->getStyle('A1:K1')->applyFromArray($headerStyle);

    // Adăugăm datele
    $row = 2;
    foreach ($results as $dosar) {
        $searchTerm = $dosar->searchTerm ?? '';
        $searchType = $dosar->searchType ?? '';

        // FIXED: Ensure parti is an array for the functions
        $parti = $dosar->parti ?? [];
        if (!is_array($parti)) {
            $parti = [];
        }

        // Get proper institution name using the mapping
        $institutie = $dosar->institutie ?? '';
        $instituteName = !empty($institutie) ? ($institutii[$institutie] ?? $institutie) : '';

        // Convert search type to Romanian
        $searchTypeRo = '';
        switch ($searchType) {
            case 'numeParte':
                $searchTypeRo = 'Nume parte';
                break;
            case 'numarDosar':
                $searchTypeRo = 'Număr dosar';
                break;
            case 'obiectDosar':
                $searchTypeRo = 'Obiect dosar';
                break;
            default:
                $searchTypeRo = 'Necunoscut';
        }

        $sheet->setCellValue("A{$row}", $searchTerm);
        $sheet->setCellValue("B{$row}", $searchTypeRo);
        $sheet->setCellValue("C{$row}", $dosar->numar ?? '');
        $sheet->setCellValue("D{$row}", $instituteName);
        $sheet->setCellValue("E{$row}", $dosar->obiect ?? '');
        $sheet->setCellValue("F{$row}", $dosar->stadiuProcesual ?? '');
        $sheet->setCellValue("G{$row}", $dosar->data ?? '');
        $sheet->setCellValue("H{$row}", $dosar->dataModificare ?? '');
        $sheet->setCellValue("I{$row}", $dosar->categorieCaz ?? '');
        $sheet->setCellValue("J{$row}", getRelevantPartyName($parti, $searchTerm, $searchType) ?: '');
        $sheet->setCellValue("K{$row}", getRelevantPartyQuality($parti, $searchTerm, $searchType) ?: '');
        $row++;
    }

    // Auto-dimensionare coloane
    foreach (range('A', 'K') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }

    // Setăm header-ele pentru descărcare
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header("Content-Disposition: attachment; filename=\"{$filename}\"");
    header('Cache-Control: max-age=0');

    // Generăm și trimitem fișierul
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    $writer->save('php://output');
}

/**
 * Obține numele părții relevante din lista de părți
 * Caută partea care se potrivește cu termenul de căutare
 * FIXED: Adaptată pentru structura de array din DosarService.php
 */
function getRelevantPartyName($parti, $searchTerm = '', $searchType = '') {
    if (empty($parti) || !is_array($parti)) {
        return '';
    }

    // Pentru căutarea după nume parte, încercăm să găsim potrivirea
    if (!empty($searchTerm) && $searchType === 'numeParte') {
        $matchingParty = findMatchingParty($parti, $searchTerm);

        if ($matchingParty) {
            // FIXED: Parties are arrays, not objects (from DosarService.php line 537-540)
            return $matchingParty['nume'] ?? '';
        }
    }

    // Fallback: returnăm prima parte
    $firstParty = reset($parti);
    if ($firstParty) {
        // FIXED: Parties are arrays, not objects
        return $firstParty['nume'] ?? '';
    }

    return '';
}

/**
 * Obține calitatea părții relevante din lista de părți
 * Returnează calitatea părții care se potrivește cu termenul de căutare
 * FIXED: Adaptată pentru structura de array din DosarService.php
 */
function getRelevantPartyQuality($parti, $searchTerm = '', $searchType = '') {
    if (empty($parti) || !is_array($parti)) {
        return '';
    }

    // Pentru căutarea după nume parte, încercăm să găsim potrivirea
    if (!empty($searchTerm) && $searchType === 'numeParte') {
        $matchingParty = findMatchingParty($parti, $searchTerm);
        if ($matchingParty) {
            // FIXED: Parties are arrays, not objects (from DosarService.php line 537-540)
            return $matchingParty['calitate'] ?? '';
        }
    }

    // Fallback: returnăm prima parte
    $firstParty = reset($parti);
    if ($firstParty) {
        // FIXED: Parties are arrays, not objects
        return $firstParty['calitate'] ?? '';
    }

    return '';
}

/**
 * Găsește partea care se potrivește cu termenul de căutare
 * ENHANCED: Exact phrase matching + multi-word support + Romanian diacritics
 */
function findMatchingParty($parti, $searchTerm) {
    if (empty($parti) || empty($searchTerm)) {
        return null;
    }

    // Debug logging for NORDIS cases
    $isNordisCase = stripos($searchTerm, 'NORDIS') !== false;
    if ($isNordisCase) {
        error_log("FINDMATCHINGPARTY DEBUG: SearchTerm='$searchTerm', PartiCount=" . count($parti));
    }

    // Detectăm dacă avem căutare cu ghilimele pentru potrivire exactă de frază
    $isExactPhraseSearch = false;
    $cleanSearchTerm = $searchTerm;

    if (preg_match('/^"(.+)"$/', trim($searchTerm), $matches)) {
        $isExactPhraseSearch = true;
        $cleanSearchTerm = $matches[1];
        if ($isNordisCase) {
            error_log("FINDMATCHINGPARTY DEBUG: Detected quoted search, CleanTerm='$cleanSearchTerm'");
        }
    }

    // Normalizăm termenul de căutare pentru comparații
    $normalizedSearchTerm = normalizeForSearch($cleanSearchTerm);
    $lowerSearchTerm = mb_strtolower(trim($cleanSearchTerm), 'UTF-8');

    // PHASE 1: Căutare exactă de frază (prioritate maximă pentru ghilimele)
    if ($isExactPhraseSearch) {
        foreach ($parti as $party) {
            $partyName = extractPartyName($party);
            if (empty($partyName)) {
                continue;
            }

            // Verificare exactă completă pentru frază
            $normalizedPartyName = normalizeForSearch($partyName);
            if (strcasecmp($normalizedPartyName, $normalizedSearchTerm) === 0) {
                return $party;
            }

            // Verificare exactă fără normalizare
            if (strcasecmp(trim($partyName), trim($cleanSearchTerm)) === 0) {
                return $party;
            }
        }

        // Pentru căutarea cu ghilimele, căutăm și potriviri parțiale de frază
        foreach ($parti as $party) {
            $partyName = extractPartyName($party);
            if (empty($partyName)) {
                continue;
            }

            if ($isNordisCase) {
                error_log("FINDMATCHINGPARTY DEBUG: Checking party='$partyName' against search='$cleanSearchTerm'");
            }

            // Verificare dacă fraza completă se găsește în numele părții
            $normalizedPartyName = normalizeForSearch($partyName);
            if (mb_strpos($normalizedPartyName, $normalizedSearchTerm, 0, 'UTF-8') !== false) {
                if ($isNordisCase) {
                    error_log("FINDMATCHINGPARTY DEBUG: Found match (normalized) - returning party");
                }
                return $party;
            }

            $lowerPartyName = mb_strtolower(trim($partyName), 'UTF-8');
            if (mb_strpos($lowerPartyName, $lowerSearchTerm, 0, 'UTF-8') !== false) {
                if ($isNordisCase) {
                    error_log("FINDMATCHINGPARTY DEBUG: Found match (lowercase) - returning party");
                }
                return $party;
            }
        }
    }

    // PHASE 2: Căutăm potrivire exactă (pentru căutări fără ghilimele)
    foreach ($parti as $party) {
        $partyName = extractPartyName($party);
        if (empty($partyName)) {
            continue;
        }

        // Verificare exactă cu diacritice normalizate
        $normalizedPartyName = normalizeForSearch($partyName);
        if (strcasecmp($normalizedPartyName, $normalizedSearchTerm) === 0) {
            return $party;
        }

        // Verificare exactă fără normalizare (pentru cazuri perfecte)
        if (strcasecmp(trim($partyName), trim($cleanSearchTerm)) === 0) {
            return $party;
        }
    }

    // PHASE 3: Căutăm potrivire parțială (fallback)
    foreach ($parti as $party) {
        $partyName = extractPartyName($party);
        if (empty($partyName)) {
            continue;
        }

        // Verificare parțială cu diacritice normalizate
        $normalizedPartyName = normalizeForSearch($partyName);
        if (mb_strpos($normalizedPartyName, $normalizedSearchTerm, 0, 'UTF-8') !== false) {
            return $party;
        }

        // Verificare parțială fără normalizare (pentru cazuri cu diacritice exacte)
        $lowerPartyName = mb_strtolower(trim($partyName), 'UTF-8');
        if (mb_strpos($lowerPartyName, $lowerSearchTerm, 0, 'UTF-8') !== false) {
            return $party;
        }
    }

    return null;
}

/**
 * Extrage numele părții din structura de date (array sau object)
 * Helper function pentru compatibilitate cu diferite structuri
 */
function extractPartyName($party) {
    if (is_array($party) && isset($party['nume'])) {
        return $party['nume'];
    } elseif (is_object($party) && isset($party->nume)) {
        return $party->nume;
    }
    return '';
}

/**
 * Normalizează textul pentru căutare, gestionând diacriticele românești
 * Versiune îmbunătățită care combină logica din search.php cu suportul pentru diacritice
 */
function normalizeForSearch($text) {
    if (empty($text)) return '';

    // Convertim la string și eliminăm spațiile de la început și sfârșit
    $text = trim((string) $text);

    // Verificăm encoding UTF-8 (ca în search.php)
    if (!mb_check_encoding($text, 'UTF-8')) {
        $text = mb_convert_encoding($text, 'UTF-8', 'auto');
    }

    // Mapare diacritice românești (extinsă pentru compatibilitate completă)
    $diacritics = [
        'ă' => 'a', 'Ă' => 'A',
        'â' => 'a', 'Â' => 'A',
        'î' => 'i', 'Î' => 'I',
        'ș' => 's', 'Ș' => 'S',
        'ț' => 't', 'Ț' => 'T',
        // Adăugăm și alte variante care pot apărea
        'ş' => 's', 'Ş' => 'S',  // s-cedilla (varianta veche)
        'ţ' => 't', 'Ţ' => 'T'   // t-cedilla (varianta veche)
    ];

    // Înlocuim diacriticele
    $normalized = str_replace(array_keys($diacritics), array_values($diacritics), $text);

    // Eliminăm spațiile multiple și convertim la lowercase pentru comparație
    // Folosim funcții UTF-8 aware ca în search.php
    $normalized = preg_replace('/\s+/u', ' ', $normalized);
    $normalized = mb_strtolower($normalized, 'UTF-8');

    return $normalized;
}

/**
 * Verifică dacă un nume de parte se potrivește cu termenul de căutare
 * și returnează numele cu evidențierea potrivirii
 * ENHANCED: Exact phrase matching + multi-word highlighting + Romanian diacritics
 */
function highlightMatchingPartyName($partyName, $searchTerm, $searchType) {
    if (empty($partyName) || empty($searchTerm) || $searchType !== 'numeParte') {
        return htmlspecialchars($partyName);
    }

    // Detectăm dacă avem căutare cu ghilimele pentru potrivire exactă de frază
    $isExactPhraseSearch = false;
    $cleanSearchTerm = $searchTerm;

    if (preg_match('/^"(.+)"$/', trim($searchTerm), $matches)) {
        $isExactPhraseSearch = true;
        $cleanSearchTerm = $matches[1];

        // Enhanced debug for quoted searches
        if (stripos($searchTerm, 'NORDIS') !== false) {
            error_log("HIGHLIGHTMATCHINGPARTY DEBUG: Detected quoted search");
            error_log("HIGHLIGHTMATCHINGPARTY DEBUG: Original='$searchTerm', Clean='$cleanSearchTerm'");
        }
    }

    // Verificăm tipul de potrivire pentru styling diferențiat
    $matchType = getMatchType($partyName, $cleanSearchTerm, $isExactPhraseSearch);

    // Enhanced debug logging for quoted searches
    if ($isExactPhraseSearch) {
        // Always log for debugging this specific issue
        error_log("DEBUG QUOTED SEARCH: Party='$partyName', SearchTerm='$cleanSearchTerm', MatchType='$matchType', IsExactPhrase=" . ($isExactPhraseSearch ? 'true' : 'false'));

        // Additional debug for NORDIS cases
        if (stripos($partyName, 'NORDIS') !== false || stripos($cleanSearchTerm, 'NORDIS') !== false) {
            error_log("NORDIS DEBUG: Party='$partyName', SearchTerm='$cleanSearchTerm', MatchType='$matchType'");
            error_log("NORDIS DEBUG: Normalized Party='" . normalizeForSearch($partyName) . "', Normalized Search='" . normalizeForSearch($cleanSearchTerm) . "'");
        }
    }

    if ($matchType === 'none') {
        return htmlspecialchars($partyName);
    }

    // Evidențiem termenul/fraza în funcție de tipul de căutare
    if ($isExactPhraseSearch) {
        $highlightedName = highlightExactPhrase($partyName, $cleanSearchTerm);
    } else {
        $highlightedName = highlightSearchTerm($partyName, $cleanSearchTerm);

        // Dacă nu am reușit să evidențiem, încercăm cu normalizare
        if ($highlightedName === htmlspecialchars($partyName)) {
            $highlightedName = highlightSearchTermNormalized($partyName, $cleanSearchTerm);
        }
    }

    // Aplicăm styling în funcție de tipul de potrivire
    $containerClass = 'matching-party-name party-match-indicator';
    $tooltip = '';

    if ($matchType === 'exact') {
        $containerClass .= ' exact-match';
        $tooltip = '✓ Potrivire exactă';
    } elseif ($matchType === 'exact-phrase') {
        $containerClass .= ' exact-phrase-match';
        $tooltip = '✓ Potrivire exactă de frază';
    } elseif ($matchType === 'partial-phrase') {
        $containerClass .= ' partial-match';
        $tooltip = '✓ Potrivire parțială de frază';
    } elseif ($matchType === 'partial') {
        $containerClass .= ' partial-match';
        $tooltip = '✓ Potrivire parțială';
    }

    $titleAttribute = !empty($tooltip) ? ' title="' . htmlspecialchars($tooltip) . '"' : '';
    return '<span class="' . $containerClass . '"' . $titleAttribute . '>' . $highlightedName . '</span>';
}

/**
 * Determină tipul de potrivire între numele părții și termenul de căutare
 * ENHANCED: With debugging for NORDIS MANAGEMENT case
 */
function getMatchType($partyName, $searchTerm, $isExactPhraseSearch = false) {
    $normalizedPartyName = normalizeForSearch($partyName);
    $normalizedSearchTerm = normalizeForSearch($searchTerm);

    // Debug logging for NORDIS cases
    $isNordisCase = (stripos($partyName, 'NORDIS') !== false || stripos($searchTerm, 'NORDIS') !== false);

    if ($isNordisCase) {
        error_log("GETMATCHTYPE DEBUG: Party='$partyName', SearchTerm='$searchTerm', IsExactPhrase=" . ($isExactPhraseSearch ? 'true' : 'false'));
        error_log("GETMATCHTYPE DEBUG: NormalizedParty='$normalizedPartyName', NormalizedSearch='$normalizedSearchTerm'");
    }

    // Pentru căutarea cu ghilimele, verificăm potrivirea exactă de frază
    if ($isExactPhraseSearch) {
        // Verificare exactă completă pentru frază (numele părții este exact fraza căutată)
        if (strcasecmp($normalizedPartyName, $normalizedSearchTerm) === 0) {
            if ($isNordisCase) error_log("GETMATCHTYPE DEBUG: Found exact-phrase match (normalized)");
            return 'exact-phrase';
        }

        // Verificare exactă fără normalizare (pentru cazuri perfecte)
        if (strcasecmp(trim($partyName), trim($searchTerm)) === 0) {
            if ($isNordisCase) error_log("GETMATCHTYPE DEBUG: Found exact-phrase match (direct)");
            return 'exact-phrase';
        }

        // Verificare dacă fraza se găsește în numele părții (partial phrase match)
        if (mb_strpos($normalizedPartyName, $normalizedSearchTerm, 0, 'UTF-8') !== false) {
            if ($isNordisCase) error_log("GETMATCHTYPE DEBUG: Found partial-phrase match (normalized)");
            return 'partial-phrase';
        }

        // Verificare parțială fără normalizare (pentru cazuri cu diacritice exacte)
        $lowerPartyName = mb_strtolower(trim($partyName), 'UTF-8');
        $lowerSearchTerm = mb_strtolower(trim($searchTerm), 'UTF-8');
        if (mb_strpos($lowerPartyName, $lowerSearchTerm, 0, 'UTF-8') !== false) {
            if ($isNordisCase) error_log("GETMATCHTYPE DEBUG: Found partial-phrase match (lowercase)");
            return 'partial-phrase';
        }

        if ($isNordisCase) error_log("GETMATCHTYPE DEBUG: No match found");
        return 'none';
    }

    // Pentru căutarea normală (fără ghilimele)
    // Verificare exactă
    if (strcasecmp($normalizedPartyName, $normalizedSearchTerm) === 0) {
        return 'exact';
    }

    if (strcasecmp(trim($partyName), trim($searchTerm)) === 0) {
        return 'exact';
    }

    // Verificare parțială
    $lowerPartyName = mb_strtolower(trim($partyName), 'UTF-8');
    $lowerSearchTerm = mb_strtolower(trim($searchTerm), 'UTF-8');

    if (mb_strpos($normalizedPartyName, $normalizedSearchTerm, 0, 'UTF-8') !== false ||
        mb_strpos($lowerPartyName, $lowerSearchTerm, 0, 'UTF-8') !== false) {
        return 'partial';
    }

    return 'none';
}

/**
 * Evidențiază termenul de căutare în numele părții (fără normalizare)
 */
function highlightSearchTerm($partyName, $searchTerm) {
    $escapedPartyName = htmlspecialchars($partyName);

    // Încercăm evidențierea directă (case-insensitive)
    $highlighted = preg_replace(
        '/(' . preg_quote($searchTerm, '/') . ')/iu',
        '<strong class="text-primary">$1</strong>',
        $escapedPartyName
    );

    return $highlighted;
}

/**
 * Evidențiază fraza exactă în numele părții pentru căutări cu ghilimele
 * ENHANCED: Complete phrase highlighting for exact phrase searches
 */
function highlightExactPhrase($partyName, $searchPhrase) {
    $escapedPartyName = htmlspecialchars($partyName);

    // Încercăm evidențierea directă a frazei complete (case-insensitive)
    $highlighted = preg_replace(
        '/(' . preg_quote($searchPhrase, '/') . ')/iu',
        '<strong class="text-primary">$1</strong>',
        $escapedPartyName
    );

    // Dacă nu am găsit potrivire directă, încercăm cu normalizare pentru diacritice
    if ($highlighted === $escapedPartyName) {
        $pattern = buildDiacriticsPattern($searchPhrase);
        if ($pattern) {
            $highlighted = preg_replace(
                '/(' . $pattern . ')/iu',
                '<strong class="text-primary">$1</strong>',
                $escapedPartyName
            );
        }
    }

    return $highlighted;
}

/**
 * Evidențiază termenul de căutare cu normalizare pentru diacritice
 */
function highlightSearchTermNormalized($partyName, $searchTerm) {
    $escapedPartyName = htmlspecialchars($partyName);
    $normalizedSearchTerm = normalizeForSearch($searchTerm);

    // Construim un pattern care să găsească variante cu/fără diacritice
    $pattern = buildDiacriticsPattern($searchTerm);

    if ($pattern) {
        $highlighted = preg_replace(
            '/(' . $pattern . ')/iu',
            '<strong class="text-primary">$1</strong>',
            $escapedPartyName
        );

        return $highlighted;
    }

    return $escapedPartyName;
}

/**
 * Construiește un pattern regex care să găsească variante cu/fără diacritice
 */
function buildDiacriticsPattern($searchTerm) {
    // Mapare inversă pentru a găsi toate variantele posibile
    $diacriticsMap = [
        'a' => '[aăâáàä]',
        'A' => '[AĂÂÁÀÄ]',
        's' => '[sșş]',
        'S' => '[SȘŞ]',
        't' => '[tțţ]',
        'T' => '[TȚŢ]',
        'i' => '[iîíìï]',
        'I' => '[IÎÍÌÏ]'
    ];

    $pattern = '';
    $chars = mb_str_split($searchTerm, 1, 'UTF-8');

    foreach ($chars as $char) {
        if (isset($diacriticsMap[$char])) {
            $pattern .= $diacriticsMap[$char];
        } else {
            $pattern .= preg_quote($char, '/');
        }
    }

    return $pattern;
}

/**
 * Cache pentru părțile relevante pentru a evita recalcularea
 * Adaptat din search.php pentru bulk_search.php
 */
$relevantPartyCache = [];

/**
 * Debug function to check party data structure
 * TEMPORARY: For debugging party data structure issues
 */
function debugPartyStructure($parti, $dosarNumber = '') {
    if (defined('DEBUG_PARTY_STRUCTURE') && DEBUG_PARTY_STRUCTURE) {
        error_log("DEBUG PARTY STRUCTURE for dosar {$dosarNumber}:");
        error_log("Parti type: " . gettype($parti));
        error_log("Parti count: " . (is_array($parti) ? count($parti) : 'not array'));

        if (is_array($parti) && !empty($parti)) {
            $firstParty = reset($parti);
            error_log("First party type: " . gettype($firstParty));
            error_log("First party structure: " . print_r($firstParty, true));
        }
    }
}

/**
 * Test function for enhanced party matching logic
 * TEMPORARY: For verifying the smart matching functionality
 */
function testEnhancedMatching() {
    // Test data simulating real party structures
    $testParties = [
        ['nume' => 'Ionescu Maria', 'calitate' => 'Reclamant'],
        ['nume' => 'Popescu Ion', 'calitate' => 'Pârât'],
        ['nume' => 'SC EXEMPLU SRL', 'calitate' => 'Terț'],
        ['nume' => 'Ștefănescu Ana', 'calitate' => 'Martor']
    ];

    $testCases = [
        ['search' => 'Popescu', 'expected' => 'Popescu Ion', 'type' => 'partial'],
        ['search' => 'Popescu Ion', 'expected' => 'Popescu Ion', 'type' => 'exact'],
        ['search' => 'Stefanescu', 'expected' => 'Ștefănescu Ana', 'type' => 'partial'],
        ['search' => 'SC EXEMPLU', 'expected' => 'SC EXEMPLU SRL', 'type' => 'partial'],
        ['search' => 'Inexistent', 'expected' => null, 'type' => 'none']
    ];

    if (defined('DEBUG_ENHANCED_MATCHING') && DEBUG_ENHANCED_MATCHING) {
        error_log("=== TESTING ENHANCED PARTY MATCHING ===");

        foreach ($testCases as $test) {
            $result = findMatchingParty($testParties, $test['search']);
            $resultName = $result ? extractPartyName($result) : null;
            $matchType = $result ? getMatchType($resultName, $test['search']) : 'none';

            $status = ($resultName === $test['expected'] && $matchType === $test['type']) ? 'PASS' : 'FAIL';

            error_log("Test: '{$test['search']}' -> Expected: '{$test['expected']}' ({$test['type']}) | Got: '{$resultName}' ({$matchType}) | {$status}");
        }

        error_log("=== END ENHANCED MATCHING TEST ===");
    }
}

/**
 * Verifică dacă o calitate de parte se potrivește cu căutarea
 * și returnează informații despre potrivire
 */
function getPartyMatchInfo($parti, $searchTerm, $searchType) {
    if (empty($parti) || empty($searchTerm) || $searchType !== 'numeParte') {
        return [
            'hasMatch' => false,
            'matchedParty' => null,
            'totalParties' => count($parti ?: [])
        ];
    }

    $matchingParty = findMatchingParty($parti, $searchTerm);

    return [
        'hasMatch' => $matchingParty !== null,
        'matchedParty' => $matchingParty,
        'totalParties' => count($parti ?: [])
    ];
}

/**
 * Pre-calculează părțile relevante pentru toate dosarele pentru a optimiza performanța
 * Adaptat din search.php pentru bulk_search.php
 */
function preCalculateRelevantPartiesForBulk($results, $searchTerm = '', $searchType = '') {
    global $relevantPartyCache;
    $relevantParties = [];

    foreach ($results as $dosar) {
        $dosarId = ($dosar->numar ?? '') . '_' . ($dosar->institutie ?? '');
        $cacheKey = $dosarId . '_' . $searchTerm . '_' . $searchType;

        // Verificăm cache-ul pentru a evita recalcularea
        if (isset($relevantPartyCache[$cacheKey])) {
            $relevantParties[$dosarId] = $relevantPartyCache[$cacheKey];
            continue;
        }

        // Calculăm partea relevantă
        $relevantParty = null;
        if ($searchType === 'numeParte' && !empty($searchTerm)) {
            $relevantParty = findMatchingParty($dosar->parti ?? [], $searchTerm);
        }

        // Fallback la prima parte dacă nu găsim potrivire
        if (!$relevantParty && !empty($dosar->parti)) {
            $relevantParty = reset($dosar->parti);
        }

        $relevantParties[$dosarId] = $relevantParty;
        $relevantPartyCache[$cacheKey] = $relevantParty;
    }

    return $relevantParties;
}

/**
 * Generează mesajul de rezultate în română
 */
function generateResultMessage($count, $term) {
    if ($count === 0) {
        return "Nu au fost găsite rezultate pentru termenul '{$term}'";
    } elseif ($count === 1) {
        return "1 rezultat găsit pentru termenul '{$term}'";
    } else {
        return "{$count} rezultate găsite pentru termenul '{$term}'";
    }
}



?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DosareJust.ro - Portal Judiciar</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/responsive.css" rel="stylesheet">

    <style>
        /* Bulk Search Specific Styles - Matching detalii_dosar.php design */
        :root {
            --primary-blue: #007bff;
            --secondary-blue: #2c3e50;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
        }

        /* Card styling matching detalii_dosar.php */
        .streamlined-card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
            margin-bottom: 2rem;
            overflow: hidden;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .streamlined-card .card-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            border-bottom: none;
            padding: 1.25rem 1.5rem;
        }

        .streamlined-card .card-body {
            padding: 2rem;
        }

        /* Compact search form styling */
        .streamlined-card.compact-form .card-body {
            padding: 1.25rem;
        }

        .compact-form .form-label {
            margin-bottom: 0.375rem;
            font-size: 0.95rem;
        }

        .compact-form .form-control,
        .compact-form .form-select {
            margin-bottom: 0.75rem;
        }

        .compact-form .term-counter {
            margin-top: 0.5rem;
            margin-bottom: 0;
            font-size: 0.875rem;
        }

        .compact-form .row {
            margin-bottom: 0;
        }

        .compact-form .col-md-4 .mt-3 {
            margin-top: 0.75rem !important;
        }

        /* Additional compact form optimizations */
        .compact-form textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }

        .compact-form .btn {
            padding: 0.5rem 1rem;
            font-size: 0.95rem;
        }

        /* Optimize card header for compact form */
        .streamlined-card.compact-form .card-header {
            padding: 1rem 1.25rem;
        }

        .compact-form .card-header h1 {
            font-size: 1.15rem;
        }

        /* Form styling */
        .form-label {
            font-weight: 600;
            color: var(--secondary-blue);
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 6px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* Advanced filters styling */
        .advanced-filters-section {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.02) 0%, rgba(44, 62, 80, 0.01) 100%);
            border: 1px solid rgba(0, 123, 255, 0.1);
            border-radius: 8px;
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .advanced-filters-section:hover {
            border-color: rgba(0, 123, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
        }

        .advanced-filters-section h6 {
            color: var(--secondary-blue);
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .advanced-filters-section h6 i {
            color: var(--primary-blue);
            margin-right: 0.5rem;
        }

        .advanced-filters-section .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }

        .advanced-filters-section .form-select:focus {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23007bff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        }

        .advanced-filters-section .form-text {
            font-size: 0.875rem;
            color: var(--secondary-blue);
            margin-top: 0.25rem;
        }

        .advanced-filters-section .form-text i {
            color: var(--primary-blue);
            margin-right: 0.25rem;
        }

        /* Date input styling */
        .date-input {
            position: relative;
        }

        .date-input:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .date-input::placeholder {
            color: #6c757d;
            font-style: italic;
        }

        /* Date validation styling */
        .date-input.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .date-input.is-valid {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        /* Case category dropdown styling */
        #categorieCaz {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }

        #categorieCaz:focus {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23007bff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        }

        /* Footer and Page Layout Styles */
        html, body {
            height: 100%;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .content-wrapper {
            flex: 1 0 auto;
        }

        .modern-footer {
            flex-shrink: 0;
            margin-top: auto;
            background-color: #f8f9fa;
            border-top: 2px solid #007bff;
            padding: 2rem 0 1rem;
            color: #6c757d;
        }

        .modern-footer .footer-links {
            display: flex;
            gap: 1.5rem;
            justify-content: flex-end;
        }

        .modern-footer .footer-link {
            color: #6c757d;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .modern-footer .footer-link:hover {
            color: #007bff;
            text-decoration: none;
        }

        /* Back to Top Button */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 50px;
            height: 50px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
        }

        .back-to-top:focus {
            outline: 2px solid #007bff;
            outline-offset: 2px;
        }

        @media (max-width: 767.98px) {
            .modern-footer .footer-links {
                flex-direction: column;
                gap: 0.75rem;
                text-align: center;
                justify-content: center;
            }

            .modern-footer .col-md-6 {
                text-align: center !important;
            }

            .back-to-top {
                bottom: 1rem;
                right: 1rem;
                width: 45px;
                height: 45px;
            }
        }

        /* Advanced Filters Toggle Styling */
        #advancedFiltersToggle {
            text-decoration: none;
            padding: 0.75rem 1rem;
            border: 1px solid var(--primary-blue);
            border-radius: 6px;
            background-color: rgba(0, 123, 255, 0.05);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        #advancedFiltersToggle:hover {
            background-color: rgba(0, 123, 255, 0.1);
            text-decoration: none;
            color: var(--primary-blue) !important;
            transform: translateY(-1px);
        }

        #advancedFiltersToggle i.fa-chevron-down {
            transition: transform 0.3s ease;
        }

        #advancedFiltersToggle.expanded i.fa-chevron-down {
            transform: rotate(180deg);
        }

        /* Advanced Filters Collapsible Section */
        #advancedFilters {
            overflow: hidden;
            transition: all 0.3s ease;
            max-height: 0;
            opacity: 0;
            padding: 0;
            margin-top: 0;
            border: none;
            background-color: transparent;
        }

        #advancedFilters.show {
            max-height: 1000px;
            opacity: 1;
            padding: 1.25rem;
            margin-top: 1.25rem;
            border: 1px solid var(--gray-200, #e9ecef);
            border-radius: 8px;
            background-color: rgba(0, 123, 255, 0.02);
        }

        /* Mobile Layout Fix for Search Button Position */
        @media (max-width: 767px) {
            /* Hide the desktop search button container on mobile */
            .card .card-body .mt-auto {
                display: none !important;
            }

            /* Create mobile search button container - always visible on mobile */
            .mobile-search-container {
                display: block !important;
                margin-top: 1rem;
                padding: 1rem;
                background-color: rgba(0, 123, 255, 0.02);
                border: 1px solid var(--gray-200, #e9ecef);
                border-radius: 8px;
            }
        }

        /* Hide mobile search container on desktop */
        @media (min-width: 768px) {
            .mobile-search-container {
                display: none !important;
            }
        }

        /* ===== EXPORT BUTTONS RESPONSIVE VISIBILITY ===== */

        /* Desktop: Show desktop export buttons, hide mobile export buttons */
        @media (min-width: 768px) {
            .export-buttons-desktop {
                display: block !important;
            }

            .export-buttons-mobile {
                display: none !important;
            }
        }

        /* Mobile: Hide desktop export buttons, show mobile export buttons */
        @media (max-width: 767px) {
            .export-buttons-desktop {
                display: none !important;
            }

            .export-buttons-mobile {
                display: block !important;
            }
        }

            /* Mobile search button styling */
            .mobile-search-container .btn {
                width: 100%;
                min-height: 44px;
                font-size: 1rem;
                font-weight: 600;
                padding: 0.75rem 1rem;
                background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
                border: none;
                border-radius: 6px;
                transition: all 0.3s ease;
            }

            .mobile-search-container .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
            }

            /* Ensure export buttons also move to mobile container */
            .mobile-search-container .export-buttons-inline {
                margin-top: 1rem;
            }

            .mobile-search-container .export-buttons-inline .btn {
                min-height: 44px;
                font-size: 0.9rem;
                margin-bottom: 0.5rem;
            }
        }

        /* Textarea specific styling */
        #bulkSearchTerms {
            min-height: 150px;
            resize: vertical;
            font-family: 'Courier New', monospace;
            line-height: 1.5;
        }

        /* Counter styling */
        .term-counter {
            font-size: 0.875rem;
            color: var(--secondary-blue);
            margin-top: 0.5rem;
        }

        .term-info-line {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 0.25rem;
        }

        /* Inline total results styling */
        .total-results-inline {
            font-size: 0.875rem;
            color: var(--primary-blue) !important;
            font-weight: 600 !important;
            margin-left: 0.5rem;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            background-color: rgba(0, 123, 255, 0.1);
            border: 1px solid rgba(0, 123, 255, 0.2);
            transition: all 0.3s ease;
        }

        .total-results-inline:hover {
            background-color: rgba(0, 123, 255, 0.15);
            border-color: rgba(0, 123, 255, 0.3);
        }

        /* Inline exact match filter styling - Streamlined without background */
        .exact-match-filter-inline {
            margin-top: 0.5rem;
        }

        .exact-match-filter-inline .form-check {
            margin-bottom: 0;
        }

        .exact-match-filter-inline .form-check-input {
            border-color: var(--primary-blue);
            margin-top: 0.125rem;
        }

        .exact-match-filter-inline .form-check-input:checked {
            background-color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .exact-match-filter-inline .form-check-input:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .exact-match-filter-inline .form-check-label {
            font-size: 0.875rem;
            color: var(--secondary-blue);
            font-weight: 500;
            cursor: pointer;
        }

        .exact-match-filter-inline .badge {
            font-size: 0.75rem;
        }

        /* Button styling */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            border: none;
            padding: 0.75rem 2rem;
            font-weight: 600;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
        }

        /* Sortable Table Headers */
        .sortable-header {
            cursor: pointer;
            user-select: none;
            position: relative;
            padding-right: 1.5rem !important;
            transition: all 0.2s ease;
        }

        .sortable-header:hover {
            background-color: rgba(0, 123, 255, 0.1);
            color: var(--primary-blue);
        }

        .sort-indicator {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.75rem;
            opacity: 0.6;
            transition: all 0.2s ease;
        }

        .sortable-header:hover .sort-indicator {
            opacity: 1;
        }

        .sortable-header.sort-asc .sort-indicator {
            opacity: 1;
            color: var(--primary-blue);
        }

        .sortable-header.sort-desc .sort-indicator {
            opacity: 1;
            color: var(--primary-blue);
        }

        .sort-indicator.sort-asc::before {
            content: '\f0de'; /* fa-sort-up */
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .sort-indicator.sort-desc::before {
            content: '\f0dd'; /* fa-sort-down */
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .sort-indicator.sort-none::before {
            content: '\f0dc'; /* fa-sort */
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        /* Mobile sortable headers */
        @media (max-width: 767px) {
            .sortable-header {
                padding-right: 1.2rem !important;
            }

            .sort-indicator {
                right: 0.3rem;
                font-size: 0.65rem;
            }
        }



        /* Results section styling */
        .results-section {
            margin-top: 2rem;
        }

        .term-results {
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .term-header {
            background: linear-gradient(135deg, var(--light-bg) 0%, #e9ecef 100%);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .term-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .term-content {
            padding: 1.5rem;
            background: white;
        }

        /* Expanded Layout Table - Original Font Sizes with Enhanced Dimensions */
        .table {
            margin-bottom: 0;
            font-size: 0.8rem; /* Reverted to original */
            table-layout: fixed;
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background-color: var(--light-bg);
            border-color: var(--border-color);
            color: var(--secondary-blue);
            font-weight: 600;
            padding: 0.5rem 0.3rem; /* Reverted to original */
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            line-height: 1.2;
            font-size: 0.75rem; /* Reverted to original */
        }

        .table td {
            padding: 0.5rem 0.3rem; /* Reverted to original */
            vertical-align: top;
            border-color: var(--border-color);
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            line-height: 1.3;
            font-size: 0.8rem; /* Reverted to original */
        }

        /* Expanded page layout for maximum content display */
        .container-fluid {
            max-width: none;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }

        /* Enhanced table container - expanded dimensions */
        .table-responsive {
            overflow-x: visible;
            -webkit-overflow-scrolling: touch;
            margin: 0 -0.5rem; /* Extend table beyond container padding */
        }

        /* Responsive container adjustments */
        @media (min-width: 1200px) {
            .container-fluid {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .table-responsive {
                margin: 0 -1rem;
            }
        }

        @media (min-width: 1400px) {
            .container-fluid {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
            }

            .table-responsive {
                margin: 0 -1.5rem;
            }
        }

        /* Ultra-Optimized Column Widths - Enhanced Case Number & Compact Date Display */
        .table th:nth-child(1), .table td:nth-child(1) { /* Număr Dosar - EXPANDED */
            width: 13%;
        }

        .table th:nth-child(2), .table td:nth-child(2) { /* Instanță */
            width: 8%;
        }

        .table th:nth-child(3), .table td:nth-child(3) { /* Obiect - Maximum Priority */
            width: 24%;
        }

        .table th:nth-child(4), .table td:nth-child(4) { /* Stadiu Procesual */
            width: 8%;
        }

        .table th:nth-child(5), .table td:nth-child(5) { /* Data - COMPACT */
            width: 7%;
        }

        .table th:nth-child(6), .table td:nth-child(6) { /* Ultima Modificare - COMPACT */
            width: 7%;
        }

        .table th:nth-child(7), .table td:nth-child(7) { /* Categorie caz */
            width: 7%;
        }

        .table th:nth-child(8), .table td:nth-child(8) { /* Nume Parte - High Priority */
            width: 15%;
        }

        .table th:nth-child(9), .table td:nth-child(9) { /* Calitate */
            width: 7%;
        }

        .table th:nth-child(10), .table td:nth-child(10) { /* Acțiuni */
            width: 9%;
        }

        /* Responsive styling for inline total results and filter */
        @media (max-width: 992px) {
            .total-results-inline {
                font-size: 0.8rem;
                padding: 0.15rem 0.4rem;
                margin-left: 0.4rem;
            }

            .exact-match-filter-inline {
                margin-top: 0.4rem;
            }

            .exact-match-filter-inline .form-check-label {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 768px) {
            .total-results-inline {
                font-size: 0.75rem;
                padding: 0.1rem 0.3rem;
                margin-left: 0.3rem;
                display: block;
                margin-top: 0.3rem;
                margin-left: 0;
                width: fit-content;
            }

            .exact-match-filter-inline {
                margin-top: 0.4rem;
            }

            .exact-match-filter-inline .form-check-label {
                font-size: 0.75rem;
            }
        }

        @media (max-width: 576px) {
            .total-results-inline {
                font-size: 0.7rem;
                padding: 0.08rem 0.25rem;
            }

            .exact-match-filter-inline {
                margin-top: 0.3rem;
            }

            .exact-match-filter-inline .form-check-label {
                font-size: 0.7rem;
            }

            .exact-match-filter-inline .badge {
                font-size: 0.65rem;
            }

            .export-buttons-inline .btn {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
            }
        }

        /* Additional responsive styling for export buttons */
        @media (max-width: 768px) {
            .export-buttons-inline .btn {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }
        }

        @media (max-width: 576px) {
            .export-buttons-inline .btn {
                font-size: 0.7rem;
                padding: 0.35rem 0.7rem;
            }

            .export-buttons-inline .filter-indicator {
                font-size: 0.65rem;
            }
        }

        /* ===== COMPACT RESULTS INFORMATION STYLES (from detalii_dosar.php) ===== */

        .results-info-compact {
            padding: 0.5rem 0.75rem;
            background-color: rgba(108, 117, 125, 0.05);
            border-left: 2px solid rgba(108, 117, 125, 0.2);
            border-radius: 0 3px 3px 0;
            margin: 0.75rem 0;
        }

        .results-info-compact .text-muted {
            color: #6c757d !important;
            font-size: 0.8rem;
            line-height: 1.4;
            margin: 0;
        }

        .results-info-compact i {
            font-size: 0.75rem;
            opacity: 0.6;
        }

        /* Mobile optimization for compact info */
        @media (max-width: 767.98px) {
            .results-info-compact {
                padding: 0.375rem 0.5rem;
                margin: 0.5rem 0;
            }

            .results-info-compact .text-muted {
                font-size: 0.75rem;
            }

            .results-info-compact i {
                font-size: 0.7rem;
            }
        }

        /* ===== MOBILE CARD VIEW STYLES (from search.php) ===== */

        /* Card view for mobile results - hidden by default */
        .card-view {
            display: none;
        }

        /* Table container - shown by default */
        .table-container {
            display: block;
        }

        /* Mobile breakpoint - switch to card view */
        @media (max-width: 767.98px) {
            /* Hide table and show card view on mobile */
            .table-container {
                display: none !important;
            }

            .card-view {
                display: block !important;
            }

            /* Mobile card styling */
            .result-card {
                border: 1px solid rgba(0,0,0,.125);
                border-radius: 8px;
                padding: 1rem;
                margin-bottom: 1rem;
                background-color: #fff;
                box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
            }

            .result-card-header {
                font-weight: bold;
                margin-bottom: 0.75rem;
                font-size: 1.1rem;
                color: var(--primary-blue);
                border-bottom: 1px solid var(--border-color);
                padding-bottom: 0.5rem;
            }

            .result-card-body {
                margin-bottom: 0.75rem;
            }

            .result-card-item {
                display: flex;
                margin-bottom: 0.5rem;
                align-items: flex-start;
            }

            .result-card-label {
                font-weight: 600;
                min-width: 140px;
                color: var(--secondary-blue);
                font-size: 0.9rem;
            }

            .result-card-value {
                flex: 1;
                word-wrap: break-word;
                overflow-wrap: break-word;
                font-size: 0.9rem;
            }

            .result-card-actions {
                margin-top: 0.75rem;
                text-align: right;
                border-top: 1px solid var(--border-color);
                padding-top: 0.75rem;
            }

            .result-card-actions .btn {
                width: auto;
                font-size: 0.85rem;
                padding: 0.5rem 1rem;
            }

            /* Mobile-specific styles for case number links */
            .case-number-link {
                font-size: 0.9rem;
                padding: 4px 6px;
                min-height: 44px; /* Ensure touch-friendly target */
                display: inline-flex;
                align-items: center;
            }

            .result-card-header .case-number-link {
                font-size: 1rem;
                font-weight: 600;
            }

            /* Enhanced mobile styles for details button in card actions */
            .result-card-actions {
                text-align: center; /* Center the button */
                padding: 0.75rem 1rem; /* Add more padding around the button area */
            }

            .result-card-actions .btn {
                width: 80% !important; /* Take up more horizontal space */
                max-width: 280px; /* Reasonable maximum width */
                min-width: 200px; /* Minimum width for consistency */
                height: 48px; /* Ensure touch-friendly height */
                padding: 0.75rem 1.5rem; /* Generous padding for better touch target */
                border-radius: 8px; /* Slightly more rounded for modern look */
                font-size: 0.95rem !important; /* Slightly larger font */
                font-weight: 600; /* Make text more prominent */
                display: flex !important; /* Use flexbox for better alignment */
                align-items: center;
                justify-content: center;
                gap: 0.5rem; /* Space between icon and text */
                background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important; /* Enhanced gradient */
                border: 2px solid #007bff !important;
                box-shadow: 0 2px 8px rgba(0, 123, 255, 0.25); /* More prominent shadow */
                transition: all 0.3s ease;
                color: #ffffff !important;
            }

            .result-card-actions .btn:hover {
                background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
                border-color: #0056b3 !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 123, 255, 0.35);
                color: #ffffff !important;
            }

            .result-card-actions .btn:active {
                transform: translateY(0);
                box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
            }

            .result-card-actions .btn i {
                font-size: 1rem; /* Slightly larger icon */
                color: #ffffff;
            }
        }

        /* Extra small mobile optimization */
        @media (max-width: 575.98px) {
            .result-card {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
            }

            .result-card-header {
                font-size: 1rem;
                margin-bottom: 0.5rem;
            }

            .result-card-label {
                min-width: 120px;
                font-size: 0.85rem;
            }

            .result-card-value {
                font-size: 0.85rem;
            }

            .result-card-item {
                margin-bottom: 0.4rem;
            }

            .result-card-actions {
                margin-top: 0.5rem;
                padding-top: 0.5rem;
            }

            .result-card-actions .btn {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
            }

            /* Override smaller screen styles for result-card-actions specifically */
            .result-card-actions .btn {
                width: 90% !important; /* Override for very small screens */
                min-width: 180px !important; /* Ensure minimum width even on very small screens */
                font-size: 0.9rem !important;
                padding: 0.65rem 1rem !important;
            }

            /* Adjust for very small screens (less than 400px) */
            @media (max-width: 399px) {
                .result-card-actions .btn {
                    width: 95% !important;
                    min-width: 160px !important;
                    font-size: 0.85rem !important;
                    padding: 0.6rem 0.8rem !important;
                }
            }
        }

        /* Reverted Font Sizes - Original Values with Enhanced Layout */
        @media (max-width: 1200px) {
            .table {
                font-size: 0.75rem; /* Reverted to original */
            }

            .table th, .table td {
                padding: 0.4rem 0.25rem; /* Reverted to original */
                line-height: 1.2;
            }

            .table th {
                font-size: 0.7rem; /* Reverted to original */
            }

            /* Large tablet optimization - Enhanced case number & compact date columns */
            .table th:nth-child(1), .table td:nth-child(1) { width: 12%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 7%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 25%; }
            .table th:nth-child(4), .table td:nth-child(4) { width: 7%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 7%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 7%; }
            .table th:nth-child(7), .table td:nth-child(7) { width: 6%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 16%; }
            .table th:nth-child(9), .table td:nth-child(9) { width: 6%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 10%; }
        }

        @media (max-width: 992px) {
            .table {
                font-size: 0.7rem; /* Reverted to original */
            }

            .table th, .table td {
                padding: 0.35rem 0.2rem; /* Reverted to original */
                line-height: 1.15;
            }

            .table th {
                font-size: 0.65rem; /* Reverted to original */
            }

            /* Tablet optimization - enhanced case number & compact date visibility */
            .table th:nth-child(1), .table td:nth-child(1) { width: 11%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 6%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 26%; }
            .table th:nth-child(4), .table td:nth-child(4) { width: 6%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 6%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 6%; }
            .table th:nth-child(7), .table td:nth-child(7) { width: 6%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 17%; }
            .table th:nth-child(9), .table td:nth-child(9) { width: 5%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 11%; }
        }

        @media (max-width: 768px) {
            .table {
                font-size: 0.65rem; /* Reverted to original */
            }

            .table th, .table td {
                padding: 0.25rem 0.15rem; /* Reverted to original */
                line-height: 1.1;
            }

            .table th {
                font-size: 0.6rem; /* Reverted to original */
                padding: 0.2rem 0.1rem; /* Reverted to original */
            }

            /* Mobile optimization - enhanced case number & compact date readability */
            .table th:nth-child(1), .table td:nth-child(1) { width: 10%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 5%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 28%; }
            .table th:nth-child(4), .table td:nth-child(4) { width: 5%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 5%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 5%; }
            .table th:nth-child(7), .table td:nth-child(7) { width: 5%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 18%; }
            .table th:nth-child(9), .table td:nth-child(9) { width: 5%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 12%; }
        }

        @media (max-width: 576px) {
            .table {
                font-size: 0.6rem; /* Reverted to original */
            }

            .table th, .table td {
                padding: 0.2rem 0.1rem; /* Reverted to original */
                line-height: 1.05;
            }

            .table th {
                font-size: 0.55rem; /* Reverted to original */
                padding: 0.15rem 0.08rem; /* Reverted to original */
            }

            /* Small mobile - optimized case number & compact date display */
            .table th:nth-child(1), .table td:nth-child(1) { width: 9%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 4%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 30%; }
            .table th:nth-child(4), .table td:nth-child(4) { width: 4%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 4%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 4%; }
            .table th:nth-child(7), .table td:nth-child(7) { width: 4%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 20%; }
            .table th:nth-child(9), .table td:nth-child(9) { width: 4%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 15%; }
        }

        /* Reverted ultra-small mobile (320px) optimization - Original font sizes */
        @media (max-width: 320px) {
            .table {
                font-size: 0.55rem; /* Reverted to original */
            }

            .table th, .table td {
                padding: 0.15rem 0.05rem; /* Reverted to original */
                line-height: 1.0;
            }

            .table th {
                font-size: 0.5rem; /* Reverted to original */
                padding: 0.1rem 0.05rem; /* Reverted to original */
            }

            /* iPhone SE and ultra-small screens - optimized case number & minimum date visibility */
            .table th:nth-child(1), .table td:nth-child(1) { width: 8%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 3%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 32%; }
            .table th:nth-child(4), .table td:nth-child(4) { width: 3%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 3%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 3%; }
            .table th:nth-child(7), .table td:nth-child(7) { width: 3%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 22%; }
            .table th:nth-child(9), .table td:nth-child(9) { width: 3%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 16%; }
        }

        /* ===== MOBILE TABLE OPTIMIZATIONS (from search.php) ===== */

        /* Mobile Large (576px-767px) - ULTRA COMPACT but readable layout */
        @media (min-width: 576px) and (max-width: 767px) {
            .table thead th {
                font-size: 0.7rem;
                padding: 0.4rem 0.3rem;
                /* ULTRA COMPACT TEXT WRAPPING for mobile large header visibility */
                white-space: normal;
                word-wrap: break-word;
                overflow-wrap: break-word;
                line-height: 1.0;
                min-height: 2rem;
                max-height: 3rem;
            }

            /* Ultra compact text wrapping on mobile large */
            .header-text,
            th a span:first-child {
                white-space: normal;
                overflow: visible;
                text-overflow: unset;
                word-wrap: break-word;
                overflow-wrap: break-word;
                font-size: 0.7rem;
                line-height: 1.0;
                max-height: 2.2rem;
            }

            /* Fixed table layout with compact percentages */
            .table {
                table-layout: fixed;
                width: 100%;
            }

            .table th:nth-child(1),  /* Número dosar - INCREASED for full visibility */
            .table td:nth-child(1) {
                width: 10%;
                min-width: 0;
            }

            .table th:nth-child(2),  /* Instanță */
            .table td:nth-child(2) {
                width: 11%;
                min-width: 0;
            }

            .table th:nth-child(3),  /* Obiect */
            .table td:nth-child(3) {
                width: 25%;
                min-width: 0;
            }

            .table th:nth-child(4),  /* Stadiu Procesual */
            .table td:nth-child(4) {
                width: 9%;
                min-width: 0;
            }

            .table th:nth-child(5),  /* Data */
            .table td:nth-child(5) {
                width: 8%;
                min-width: 0;
            }

            .table th:nth-child(6),  /* Ultima Modificare */
            .table td:nth-child(6) {
                width: 8%;
                min-width: 0;
            }

            .table th:nth-child(7),  /* Categorie caz */
            .table td:nth-child(7) {
                width: 7%;
                min-width: 0;
            }

            .table th:nth-child(8),  /* Nume Parte */
            .table td:nth-child(8) {
                width: 14%;
                min-width: 0;
            }

            .table th:nth-child(9),  /* Calitate */
            .table td:nth-child(9) {
                width: 6%;
                min-width: 0;
            }

            .table th:nth-child(10), /* Acțiuni - MAINTAINED for action buttons */
            .table td:nth-child(10) {
                width: 8%;
                min-width: 0;
                text-align: center;
            }
        }

        /* Mobile Small (≤575px) - MINIMAL compact layout */
        @media (max-width: 575px) {
            .table thead th {
                font-size: 0.65rem;
                padding: 0.3rem 0.2rem;
                /* MINIMAL TEXT WRAPPING for mobile small header visibility */
                white-space: normal;
                word-wrap: break-word;
                overflow-wrap: break-word;
                line-height: 1.0;
                min-height: 1.8rem;
                max-height: 2.5rem;
            }

            /* Minimal text wrapping on small mobile */
            .header-text,
            th a span:first-child {
                white-space: normal;
                overflow: visible;
                text-overflow: unset;
                line-height: 1.0;
                word-wrap: break-word;
                overflow-wrap: break-word;
                font-size: 0.65rem;
                max-height: 2rem;
            }

            /* Fixed table layout with minimal percentages */
            .table {
                table-layout: fixed;
                width: 100%;
            }

            .table th:nth-child(1),  /* Număr dosar */
            .table td:nth-child(1) {
                width: 9%;
                min-width: 0;
            }

            .table th:nth-child(2),  /* Instanță */
            .table td:nth-child(2) {
                width: 10%;
                min-width: 0;
            }

            .table th:nth-child(3),  /* Obiect */
            .table td:nth-child(3) {
                width: 26%;
                min-width: 0;
            }

            .table th:nth-child(4),  /* Stadiu Procesual */
            .table td:nth-child(4) {
                width: 8%;
                min-width: 0;
            }

            .table th:nth-child(5),  /* Data */
            .table td:nth-child(5) {
                width: 7%;
                min-width: 0;
            }

            .table th:nth-child(6),  /* Ultima Modificare */
            .table td:nth-child(6) {
                width: 7%;
                min-width: 0;
            }

            .table th:nth-child(7),  /* Categorie caz */
            .table td:nth-child(7) {
                width: 6%;
                min-width: 0;
            }

            .table th:nth-child(8),  /* Nume Parte */
            .table td:nth-child(8) {
                width: 15%;
                min-width: 0;
            }

            .table th:nth-child(9),  /* Calitate */
            .table td:nth-child(9) {
                width: 6%;
                min-width: 0;
            }

            .table th:nth-child(10), /* Acțiuni */
            .table td:nth-child(10) {
                width: 8%;
                min-width: 0;
                text-align: center;
            }
        }

        /* Responsive text adjustments for mobile */
        @media (max-width: 767px) {
            .table td,
            .table th {
                line-height: 1.3;
                font-size: 0.85rem;
            }

            .header-text {
                word-break: break-word;
                overflow-wrap: break-word;
                line-height: 1.2;
            }
        }

        @media (max-width: 575px) {
            .table td,
            .table th {
                line-height: 1.2;
                font-size: 0.8rem;
                padding: 0.5rem 0.25rem;
            }

            .header-text {
                font-size: 0.7rem;
                line-height: 1.1;
            }
        }

        /* Enhanced readability for all screen sizes */
        .table th {
            font-size: 0.8em;
            text-align: center;
            vertical-align: middle;
        }

        .table td {
            text-align: left;
            vertical-align: top;
        }

        /* Consistent Date Column Styling - Same Font as Case Number */
        .table th:nth-child(5), .table td:nth-child(5), /* Data */
        .table th:nth-child(6), .table td:nth-child(6) { /* Ultima Modificare */
            text-align: center;
            white-space: nowrap;
            font-weight: 500;
        }

        /* Action column center alignment */
        .table th:nth-child(10), .table td:nth-child(10) { /* Acțiuni */
            text-align: center;
        }

        /* Reverted date format optimization - Original font sizes */
        .table td:nth-child(5), .table td:nth-child(6) {
            font-size: 0.85em; /* Reverted to original */
            letter-spacing: 0.5px; /* Reverted to original */
        }

        /* Reverted responsive date formatting - Original font sizes */
        @media (max-width: 768px) {
            .table td:nth-child(5), .table td:nth-child(6) {
                font-size: 0.75em; /* Reverted to original */
                letter-spacing: 0.3px; /* Reverted to original */
            }
        }

        @media (max-width: 576px) {
            .table td:nth-child(5), .table td:nth-child(6) {
                font-size: 0.7em; /* Reverted to original */
                letter-spacing: 0.2px; /* Reverted to original */
            }
        }

        @media (max-width: 320px) {
            .table td:nth-child(5), .table td:nth-child(6) {
                font-size: 0.65em; /* Reverted to original */
                letter-spacing: 0.1px; /* Reverted to original */
            }
        }

        /* Reverted Action Button Styling - Original Sizes */
        .table th:nth-child(10), .table td:nth-child(10) {
            text-align: center;
            vertical-align: middle;
        }

        /* Reverted action buttons optimization - Original sizes */
        .table td:nth-child(10) .btn {
            white-space: nowrap;
            min-width: 40px; /* Reverted to original */
            min-height: 28px; /* Reverted to original */
            font-size: 0.7rem; /* Reverted to original */
            padding: 0.25rem 0.3rem; /* Reverted to original */
        }

        /* Reverted responsive button adjustments - Original sizes */
        @media (max-width: 1200px) {
            .table td:nth-child(10) .btn {
                font-size: 0.65rem; /* Reverted to original */
                padding: 0.2rem 0.25rem; /* Reverted to original */
                min-width: 38px; /* Reverted to original */
                min-height: 26px; /* Reverted to original */
            }
        }

        @media (max-width: 992px) {
            .table td:nth-child(10) .btn {
                font-size: 0.6rem; /* Reverted to original */
                padding: 0.15rem 0.2rem; /* Reverted to original */
                min-width: 36px; /* Reverted to original */
                min-height: 24px; /* Reverted to original */
            }

            .table td:nth-child(10) .btn i {
                font-size: 0.7rem; /* Reverted to original */
            }
        }

        @media (max-width: 768px) {
            .table td:nth-child(10) .btn {
                font-size: 0.55rem; /* Reverted to original */
                padding: 0.1rem 0.15rem; /* Reverted to original */
                min-width: 34px; /* Reverted to original */
                min-height: 22px; /* Reverted to original */
            }

            .table td:nth-child(10) .btn i {
                font-size: 0.65rem; /* Reverted to original */
            }
        }

        @media (max-width: 576px) {
            .table td:nth-child(10) .btn {
                font-size: 0.5rem; /* Reverted to original */
                padding: 0.08rem 0.12rem; /* Reverted to original */
                min-width: 32px; /* Reverted to original */
                min-height: 20px; /* Reverted to original */
            }

            .table td:nth-child(10) .btn i {
                font-size: 0.6rem; /* Reverted to original */
            }
        }

        @media (max-width: 320px) {
            .table td:nth-child(10) .btn {
                font-size: 0.45rem; /* Reverted to original */
                padding: 0.05rem 0.1rem; /* Reverted to original */
                min-width: 30px; /* Reverted to original */
                min-height: 18px; /* Reverted to original */
            }

            .table td:nth-child(10) .btn i {
                font-size: 0.55rem; /* Reverted to original */
            }
        }

        /* Accessibility improvements */
        .table th, .table td {
            min-height: 2.5rem;
        }

        @media (max-width: 576px) {
            .table th, .table td {
                min-height: 2rem;
            }
        }



        /* Maximum Content Visibility - No Truncation */
        .table td {
            white-space: normal !important;
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            text-align: left;
            max-height: none !important;
            overflow: visible !important;
        }

        /* Remove all text truncation and ellipsis */
        .table td .text-truncate {
            white-space: normal !important;
            overflow: visible !important;
            text-overflow: clip !important;
            max-width: none !important;
        }

        /* Priority content columns - maximum space utilization */
        .table td:nth-child(3) { /* Obiect - Full content display */
            max-height: none !important;
            overflow: visible !important;
            word-break: break-word;
        }

        .table td:nth-child(8) { /* Nume Parte - Full content display */
            max-height: none !important;
            overflow: visible !important;
            word-break: break-word;
        }

        /* Ensure all content is fully visible */
        .table td span {
            white-space: normal !important;
            overflow: visible !important;
            text-overflow: clip !important;
            max-width: none !important;
            display: block !important;
        }

        /* Reverted Party Matching Styles - Original Sizes */
        .matching-party-name {
            display: inline-block;
            padding: 0.25rem 0.5rem; /* Reverted to original */
            border-radius: 4px; /* Reverted to original */
            transition: all 0.3s ease;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        /* Exact match styling */
        .matching-party-name.exact-match {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.15) 0%, rgba(40, 167, 69, 0.1) 100%);
            border-left: 4px solid var(--success-color);
        }

        /* Reverted exact phrase match styling - Original borders */
        .matching-party-name.exact-phrase-match {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.2) 0%, rgba(23, 162, 184, 0.15) 100%);
            border-left: 4px solid var(--info-color); /* Reverted to original */
            border-radius: 6px; /* Reverted to original */
        }

        /* Reverted partial match styling - Original borders */
        .matching-party-name.partial-match {
            background-color: rgba(0, 123, 255, 0.1);
            border-left: 3px solid var(--primary-blue); /* Reverted to original */
        }

        /* Reverted hover effects for party matches - Original sizes */
        .matching-party-name:hover {
            transform: translateY(-1px); /* Reverted to original */
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2); /* Reverted to original */
        }

        /* Reverted tooltip styling for match indicators - Original sizes */
        .party-match-indicator[title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--secondary-blue);
            color: white;
            padding: 0.5rem; /* Reverted to original */
            border-radius: 4px; /* Reverted to original */
            font-size: 0.75rem; /* Reverted to original */
            white-space: nowrap;
            z-index: 1000;
        }

        /* Enhanced Party Matching Filter - Integrated with Export */
        .party-filter-integrated {
            border-top: 1px solid var(--border-color);
            padding-top: 1rem;
            margin-top: 1rem;
        }

        .party-filter-integrated .form-check {
            padding: 0.75rem;
            background-color: rgba(0, 123, 255, 0.05);
            border: 1px solid rgba(0, 123, 255, 0.1);
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .party-filter-integrated .form-check:hover {
            background-color: rgba(0, 123, 255, 0.08);
            border-color: rgba(0, 123, 255, 0.2);
        }

        .party-filter-integrated .form-check-input {
            width: 1.2em;
            height: 1.2em;
            border-color: var(--primary-blue);
        }

        .party-filter-integrated .form-check-input:checked {
            background-color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .party-filter-integrated .form-check-label {
            font-weight: 500;
            color: var(--secondary-blue);
            cursor: pointer;
        }

        .export-controls {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        /* Export button filter indicators */
        .export-btn .filter-indicator {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .export-btn.filter-active {
            position: relative;
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
        }

        .export-btn.filter-active .filter-indicator {
            display: inline !important;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 1; }
            100% { opacity: 0.8; }
        }

        #filterStatus .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
        }

        /* Responsive filter styling */
        @media (max-width: 768px) {
            .party-filter-integrated .form-check {
                padding: 0.5rem;
            }

            .party-filter-integrated .form-check-input {
                width: 1.1em;
                height: 1.1em;
            }

            .party-filter-integrated .form-check-label {
                font-size: 0.9rem;
            }

            .export-controls {
                flex-direction: column;
            }

            .export-controls .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }
        }

        /* Enhanced highlighting for search terms */
        .matching-party-name .text-primary {
            font-weight: 700;
            color: var(--primary-blue) !important;
            text-shadow: 0 1px 2px rgba(0, 123, 255, 0.3);
        }

        /* Loading overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
        }

        .loading-content {
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 400px;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            margin: 0 auto 1rem;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Progress bar */
        .progress-container {
            margin-top: 1rem;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            background-color: var(--border-color);
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* Responsive design */
        @media (max-width: 767.98px) {
            .streamlined-card .card-body {
                padding: 1rem;
            }

            .streamlined-card.compact-form .card-body {
                padding: 0.875rem;
            }

            .compact-form .form-label {
                margin-bottom: 0.25rem;
                font-size: 0.9rem;
            }

            .compact-form .form-control,
            .compact-form .form-select {
                margin-bottom: 0.5rem;
                font-size: 0.95rem;
            }

            .compact-form .term-counter {
                margin-top: 0.375rem;
                font-size: 0.8rem;
            }

            .compact-form .col-md-4 .mt-3 {
                margin-top: 0.5rem !important;
            }

            .streamlined-card.compact-form .card-header {
                padding: 0.75rem 0.875rem;
            }

            .compact-form .card-header h1 {
                font-size: 1.1rem;
            }

            .compact-form textarea.form-control {
                min-height: 100px;
            }

            .compact-form .btn {
                padding: 0.45rem 0.875rem;
                font-size: 0.9rem;
            }

            .btn-primary {
                width: 100%;
                margin-top: 1rem;
            }
        }

        /* Notification container */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        }

        /* Navigation Bar Styles */
        .navbar {
            background-color: #f8f9fa !important;
            border-bottom: 2px solid #007bff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 0.75rem 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: #2c3e50 !important;
            font-size: 1.25rem;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            color: #007bff !important;
            transform: translateY(-1px);
        }

        .navbar-brand i {
            color: #007bff;
            margin-right: 0.5rem;
        }

        .nav-link {
            color: #495057 !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 6px;
            transition: all 0.3s ease;
            margin: 0 0.25rem;
        }

        .nav-link:hover {
            color: #007bff !important;
            background-color: rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
        }

        .nav-link.active,
        .nav-item.active .nav-link {
            color: #007bff !important;
            background-color: rgba(0, 123, 255, 0.15);
            font-weight: 600;
        }

        .nav-link i {
            margin-right: 0.5rem;
            width: 16px;
            text-align: center;
        }

        /* Responsive navigation */
        @media (max-width: 991.98px) {
            .navbar-nav {
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 1px solid #dee2e6;
            }

            .nav-link {
                margin: 0.25rem 0;
                text-align: center;
            }
        }

        @media (max-width: 575.98px) {
            .navbar-brand {
                font-size: 1.1rem;
            }

            .nav-link {
                padding: 0.75rem 1rem !important;
            }
        }

        /* Results Summary Section */
        .results-summary-section {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.03) 0%, rgba(44, 62, 80, 0.02) 100%);
            border: 1px solid rgba(0, 123, 255, 0.1);
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1.5rem;
        }

        .results-info h6 {
            color: var(--secondary-blue);
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        .result-stat {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .stat-value {
            font-size: 1.1rem;
            font-weight: 700;
        }

        .filter-controls h6 {
            color: var(--secondary-blue);
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        .filter-controls .form-check {
            background-color: rgba(0, 123, 255, 0.05);
            border: 1px solid rgba(0, 123, 255, 0.15);
            border-radius: 6px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .filter-controls .form-check:hover {
            background-color: rgba(0, 123, 255, 0.08);
            border-color: rgba(0, 123, 255, 0.25);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
        }

        .filter-controls .form-check-input {
            width: 1.2em;
            height: 1.2em;
            border-color: var(--primary-blue);
            border-width: 2px;
        }

        .filter-controls .form-check-input:checked {
            background-color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .filter-controls .form-check-label {
            font-weight: 500;
            color: var(--secondary-blue);
            cursor: pointer;
            user-select: none;
        }

        /* Responsive design for results summary */
        @media (max-width: 768px) {
            .results-summary-section {
                padding: 1rem;
            }

            .results-summary-section .row {
                flex-direction: column;
            }

            .results-summary-section .col-md-4 {
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 1px solid rgba(0, 123, 255, 0.1);
            }

            .result-stat {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }

            .filter-controls .form-check {
                padding: 0.5rem;
            }

            .filter-controls .form-check-input {
                width: 1.1em;
                height: 1.1em;
            }

            .filter-controls .form-check-label {
                font-size: 0.9rem;
            }
        }

        /* Export buttons - Original styling for results section */
        .export-buttons {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        /* Stiluri pentru linkul numărului de dosar */
        .case-number-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-block;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .case-number-link:hover {
            color: #0056b3;
            text-decoration: none;
            background-color: rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
        }

        .case-number-link:focus {
            outline: 2px solid #007bff;
            outline-offset: 2px;
            background-color: rgba(0, 123, 255, 0.1);
        }

        .case-number-link:active {
            transform: translateY(0);
            background-color: rgba(0, 123, 255, 0.2);
        }

        .export-btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        /* Inline export buttons in search form */
        .export-buttons-inline {
            margin-top: 1rem;
        }

        .export-buttons-inline .btn {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
            font-weight: 600;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .export-buttons-inline .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #1e7e34 100%);
            border: none;
        }

        .export-buttons-inline .btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .export-buttons-inline .btn-info {
            background: linear-gradient(135deg, var(--info-color) 0%, #117a8b 100%);
            border: none;
            color: white;
        }

        .export-buttons-inline .btn-info:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
            color: white;
        }

        .export-buttons-inline .filter-indicator {
            font-size: 0.75rem;
        }

        /* Enhanced party name highlighting with match type differentiation */
        .matching-party-name {
            padding: 3px 6px;
            border-radius: 4px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        /* Exact match styling (highest priority) */
        .matching-party-name.exact-match {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.15) 0%, rgba(40, 167, 69, 0.1) 100%);
            border-left: 4px solid var(--success-color);
            border-radius: 4px 4px 4px 0;
        }

        /* Partial match styling */
        .matching-party-name.partial-match {
            background-color: rgba(0, 123, 255, 0.1);
            border-left: 3px solid var(--primary-blue);
            border-radius: 4px 4px 4px 0;
        }

        .matching-party-name .text-primary {
            font-weight: 600;
            color: var(--primary-blue) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced highlighting for exact matches */
        .exact-match .text-primary {
            color: var(--success-color) !important;
            font-weight: 700;
        }

        /* Tooltip for matching indication with match type */
        .party-match-indicator {
            position: relative;
            cursor: help;
        }

        .party-match-indicator::after {
            content: "✓ Potrivire găsită";
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--secondary-blue);
            color: white;
            padding: 5px 10px;
            border-radius: 6px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Different tooltips for different match types */
        .exact-match::after {
            content: "✓ Potrivire exactă";
            background-color: var(--success-color);
        }

        .partial-match::after {
            content: "✓ Potrivire parțială";
            background-color: var(--primary-blue);
        }

        .party-match-indicator:hover::after {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(-2px);
        }

        /* Hover effects for enhanced interactivity */
        .matching-party-name:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
        }

        .exact-match:hover {
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        /* Searchable Dropdown Styles */
        .searchable-select-container {
            position: relative;
            margin-bottom: 0;
        }

        .searchable-select-input {
            border-radius: 0.375rem;
            border: 1px solid #ced4da;
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            width: 100%;
        }

        .searchable-select-input:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .searchable-select-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #ffffff;
            border: 1px solid #ced4da;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            max-height: 250px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .dropdown-item {
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            color: #495057;
            text-decoration: none;
            background-color: transparent;
            border: 0;
            display: block;
            width: 100%;
            clear: both;
            font-weight: 400;
            text-align: inherit;
            white-space: nowrap;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f8f9fa;
        }

        .dropdown-item:hover,
        .dropdown-item.highlighted {
            background-color: #f8f9fa;
            color: #16181b;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item.text-muted {
            color: #6c757d !important;
            cursor: default;
        }

        .dropdown-item.text-muted:hover {
            background-color: transparent;
        }

        /* Mobile responsiveness for searchable dropdowns */
        @media (max-width: 768px) {
            .searchable-select-dropdown {
                max-height: 200px;
            }

            .dropdown-item {
                padding: 0.75rem;
                min-height: 44px;
                display: flex;
                align-items: center;
            }
        }

        /* Touch-friendly elements */
        @media (hover: none) and (pointer: coarse) {
            .dropdown-item {
                min-height: 44px;
                display: flex;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="content-wrapper">
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h5 id="loadingMessage">Se procesează căutarea...</h5>
            <p id="loadingSubmessage">Vă rugăm să așteptați...</p>
            <div class="progress-container">
                <div class="progress">
                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                </div>
                <small id="progressText" class="text-muted mt-1 d-block">0%</small>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container" style="display: none;">
        <div id="notification" class="alert" role="alert"></div>
    </div>

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-gavel me-2"></i>
                DosareJust.ro - Portal Judiciar
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-search me-1"></i>
                            Căutare Dosare
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sedinte.php">
                            <i class="fas fa-calendar-alt me-1"></i>
                            Ședințe
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-10">
                <!-- Main Search Form -->
                <div class="card streamlined-card compact-form">
                    <div class="card-header">
                        <h1 class="h4 mb-0">
                            <i class="fas fa-search-plus me-2"></i>
                            DosareJust.ro
                        </h1>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="bulkSearchForm">
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="bulkSearchTerms" class="form-label">
                                        <i class="fas fa-list me-1"></i>
                                        Termeni de căutare
                                    </label>
                                    <textarea
                                        class="form-control"
                                        id="bulkSearchTerms"
                                        name="bulkSearchTerms"
                                        placeholder="Introduceți termenii de căutare, câte unul pe linie sau separați prin virgulă:&#10;Sistemul detectează automat tipul:&#10;1234/2023 → Număr dosar&#10;Popescu Ion → Nume parte&#10;SC EXEMPLU SRL → Nume parte&#10;Folosiți ghilimele pentru căutare exactă de frază.&#10;&#10;OPȚIONAL: Puteți căuta folosind doar filtrele avansate de mai jos."><?php echo htmlspecialchars($bulkSearchTerms); ?></textarea>
                                    <div class="term-counter">
                                        <div class="term-info-line">
                                            <span id="termCount">0</span> termeni introduși
                                            <span id="termLimit" class="text-muted">(maxim 100)</span>
                                            <?php if ($hasSearchCriteria && $totalResults > 0): ?>
                                                <span class="total-results-inline text-primary fw-bold ms-2">
                                                    - Total rezultate: <span id="totalResultsCounter" data-original-total="<?php echo $totalResults; ?>"><?php echo $totalResults; ?></span>
                                                </span>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Enhanced Party Matching Filter - Shows only for party name searches -->
                                        <?php if ($hasSearchCriteria): ?>
                                            <?php
                                            // Check if any search results contain party name searches
                                            $hasPartyNameSearches = false;
                                            if (!empty($searchResults)) {
                                                foreach ($searchResults as $result) {
                                                    if (($result['type'] ?? '') === 'numeParte') {
                                                        $hasPartyNameSearches = true;
                                                        break;
                                                    }
                                                }
                                            }
                                            ?>
                                            <?php if ($hasPartyNameSearches): ?>
                                                <div class="exact-match-filter-inline mt-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="exactMatchFilter" onchange="toggleExactMatchFilter()">
                                                        <label class="form-check-label" for="exactMatchFilter">
                                                            <i class="fas fa-search me-1 text-primary"></i>
                                                            Afișează doar potriviri exacte și căutări după număr dosar
                                                        </label>
                                                    </div>
                                                    <div id="filterStatus" class="mt-2" style="display: none;">
                                                        <span class="badge bg-primary">
                                                            <i class="fas fa-filter me-1"></i>
                                                            <span id="filteredCount">0</span> rezultate filtrate
                                                        </span>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex flex-column h-100">
                                        <!-- Search Info Panel -->
                                        <div class="alert alert-info mb-3" style="flex-grow: 1;">
                                            <h6 class="alert-heading mb-2">
                                                <i class="fas fa-magic me-1"></i>
                                                Detectare automată
                                            </h6>
                                            <p class="mb-2 small">
                                                Sistemul detectează automat tipul de căutare:
                                            </p>
                                            <ul class="mb-0 small">
                                                <li><strong>Număr dosar:</strong> 1234/2023, 12345/118/2023</li>
                                                <li><strong>Nume parte:</strong> persoane, companii (SC, SRL)</li>
                                            </ul>
                                        </div>

                                        <div class="mt-auto">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fas fa-search me-2"></i>
                                                Căutare
                                            </button>
                                        </div>

                                        <!-- Export Buttons - Desktop Version -->
                                        <?php if ($hasSearchCriteria && $totalResults > 0): ?>
                                            <div class="export-buttons-inline export-buttons-desktop mt-3">
                                                <div class="d-grid gap-2">
                                                    <button type="button" class="btn btn-success export-btn" id="csvExportBtn" onclick="exportBulkResults('csv')">
                                                        <i class="fas fa-file-csv me-1"></i>
                                                        <span class="export-text">Export CSV</span>
                                                        <span class="filter-indicator" style="display: none;">
                                                            <i class="fas fa-filter ms-1"></i>
                                                        </span>
                                                    </button>
                                                    <button type="button" class="btn btn-info export-btn" id="excelExportBtn" onclick="exportBulkResults('xlsx')">
                                                        <i class="fas fa-file-excel me-1"></i>
                                                        <span class="export-text">Export Excel</span>
                                                        <span class="filter-indicator" style="display: none;">
                                                            <i class="fas fa-filter ms-1"></i>
                                                        </span>
                                                    </button>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Advanced Filters Toggle Button -->
                            <div class="mt-3">
                                <a href="#" id="advancedFiltersToggle" class="text-primary d-flex align-items-center justify-content-center">
                                    <i class="fas fa-filter me-2"></i>
                                    <span>Arată filtrele avansate</span>
                                    <i class="fas fa-chevron-down ms-2"></i>
                                </a>
                            </div>

                            <!-- Advanced Filters Section - Moved Below Search Terms -->
                            <div id="advancedFilters" class="advanced-filters-section mt-4">
                                <h6 class="mb-3">
                                    <i class="fas fa-filter me-2"></i>
                                    Filtre avansate
                                    <small class="text-muted ms-2">(opționale - pot fi folosite independent sau împreună cu termenii de căutare)</small>
                                </h6>

                                <!-- First Row of Advanced Filters -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="institutie" class="form-label">
                                            <i class="fas fa-university me-1"></i>
                                            Instanță judecătorească:
                                        </label>
                                        <div class="searchable-select-container">
                                            <input type="text" class="form-control searchable-select-input" id="institutieSearch" placeholder="Căutați și selectați o instanță..." autocomplete="off" value="<?php
                                                $selectedInstitutie = $advancedFilters['institutie'] ?? '';
                                                if (!empty($selectedInstitutie) && !empty($institutii[$selectedInstitutie])) {
                                                    echo htmlspecialchars($institutii[$selectedInstitutie]);
                                                }
                                            ?>">
                                            <select class="form-select d-none" id="institutie" name="institutie">
                                                <option value="">-- Toate instanțele --</option>
                                                <?php
                                                // Obținem valoarea selectată anterior (pentru preservarea valorii)
                                                $selectedInstitutie = $advancedFilters['institutie'] ?? '';

                                                // Verificăm dacă avem instanțe în listă
                                                if (!empty($institutii)) {
                                                    foreach ($institutii as $key => $value) {
                                                        $selected = ($key === $selectedInstitutie) ? ' selected' : '';
                                                        echo '<option value="' . htmlspecialchars($key) . '"' . $selected . '>' . htmlspecialchars($value) . '</option>';
                                                    }
                                                } else {
                                                    echo '<option value="" disabled>Nu sunt disponibile instanțe</option>';
                                                }
                                                ?>
                                            </select>
                                            <div class="searchable-select-dropdown" id="institutieDropdown"></div>
                                        </div>
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Selectați instanța pentru a filtra rezultatele după instanța judecătorească specifică
                                        </small>
                                        <small class="form-text text-warning" id="institutionWarning" style="display: none;">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            Unele coduri de instituții pot să nu fie recunoscute de API-ul SOAP. În acest caz, filtrarea se va face local.
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="categorieInstanta" class="form-label">
                                            <i class="fas fa-filter me-1"></i>
                                            Categorie instanță:
                                        </label>
                                        <select class="form-select" id="categorieInstanta" name="categorieInstanta">
                                            <option value="">-- Toate categoriile --</option>
                                            <option value="curtea_suprema" <?php echo (($advancedFilters['categorieInstanta'] ?? '') === 'curtea_suprema') ? 'selected' : ''; ?>>Înalta Curte de Casație și Justiție</option>
                                            <option value="curte_apel" <?php echo (($advancedFilters['categorieInstanta'] ?? '') === 'curte_apel') ? 'selected' : ''; ?>>Curți de Apel</option>
                                            <option value="tribunal" <?php echo (($advancedFilters['categorieInstanta'] ?? '') === 'tribunal') ? 'selected' : ''; ?>>Tribunale</option>
                                            <option value="judecatorie" <?php echo (($advancedFilters['categorieInstanta'] ?? '') === 'judecatorie') ? 'selected' : ''; ?>>Judecătorii</option>
                                        </select>
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Filtrați după tipul de instanță pentru a restrânge opțiunile
                                        </small>
                                    </div>
                                </div>

                                <!-- Second Row of Advanced Filters -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="categorieCaz" class="form-label">
                                            <i class="fas fa-gavel me-1"></i>
                                            Categorie caz:
                                        </label>
                                        <div class="searchable-select-container">
                                            <input type="text" class="form-control searchable-select-input" id="categorieCazSearch" placeholder="Căutați și selectați o categorie..." autocomplete="off" value="<?php
                                                $selectedCategory = $advancedFilters['categorieCaz'] ?? '';
                                                $categoryLabels = [
                                                    'civil' => 'Civil',
                                                    'penal' => 'Penal',
                                                    'comercial' => 'Comercial',
                                                    'contencios_administrativ' => 'Contencios Administrativ',
                                                    'fiscal' => 'Fiscal',
                                                    'munca' => 'Muncă și Asigurări Sociale',
                                                    'familie' => 'Familie și Minori',
                                                    'executare' => 'Executare',
                                                    'insolventa' => 'Insolvență'
                                                ];
                                                echo htmlspecialchars($categoryLabels[$selectedCategory] ?? '');
                                            ?>">
                                            <select class="form-select d-none" id="categorieCaz" name="categorieCaz">
                                                <option value="">-- Toate categoriile --</option>
                                                <option value="civil" <?php echo (($advancedFilters['categorieCaz'] ?? '') === 'civil') ? 'selected' : ''; ?>>Civil</option>
                                                <option value="penal" <?php echo (($advancedFilters['categorieCaz'] ?? '') === 'penal') ? 'selected' : ''; ?>>Penal</option>
                                                <option value="comercial" <?php echo (($advancedFilters['categorieCaz'] ?? '') === 'comercial') ? 'selected' : ''; ?>>Comercial</option>
                                                <option value="contencios_administrativ" <?php echo (($advancedFilters['categorieCaz'] ?? '') === 'contencios_administrativ') ? 'selected' : ''; ?>>Contencios Administrativ</option>
                                                <option value="fiscal" <?php echo (($advancedFilters['categorieCaz'] ?? '') === 'fiscal') ? 'selected' : ''; ?>>Fiscal</option>
                                                <option value="munca" <?php echo (($advancedFilters['categorieCaz'] ?? '') === 'munca') ? 'selected' : ''; ?>>Muncă și Asigurări Sociale</option>
                                                <option value="familie" <?php echo (($advancedFilters['categorieCaz'] ?? '') === 'familie') ? 'selected' : ''; ?>>Familie și Minori</option>
                                                <option value="executare" <?php echo (($advancedFilters['categorieCaz'] ?? '') === 'executare') ? 'selected' : ''; ?>>Executare</option>
                                                <option value="insolventa" <?php echo (($advancedFilters['categorieCaz'] ?? '') === 'insolventa') ? 'selected' : ''; ?>>Insolvență</option>
                                            </select>
                                            <div class="searchable-select-dropdown" id="categorieCazDropdown"></div>
                                        </div>
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Filtrați după categoria juridică a cazului
                                        </small>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="dataInceput" class="form-label">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            Data început:
                                        </label>
                                        <input type="text"
                                               class="form-control date-input"
                                               id="dataInceput"
                                               name="dataInceput"
                                               placeholder="ZZ.LL.AAAA (ex: 15.03.2023)"
                                               value="<?php echo htmlspecialchars($advancedFilters['dataInceput'] ?? ''); ?>"
                                               pattern="\d{1,2}\.\d{1,2}\.\d{4}"
                                               title="Introduceți data în format ZZ.LL.AAAA">
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Data de început pentru intervalul de căutare
                                        </small>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="dataSfarsit" class="form-label">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            Data sfârșit:
                                        </label>
                                        <input type="text"
                                               class="form-control date-input"
                                               id="dataSfarsit"
                                               name="dataSfarsit"
                                               placeholder="ZZ.LL.AAAA (ex: 31.12.2023)"
                                               value="<?php echo htmlspecialchars($advancedFilters['dataSfarsit'] ?? ''); ?>"
                                               pattern="\d{1,2}\.\d{1,2}\.\d{4}"
                                               title="Introduceți data în format ZZ.LL.AAAA">
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Data de sfârșit pentru intervalul de căutare
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Mobile Search Container - Only visible on mobile when filters are expanded -->
                            <div class="mobile-search-container">
                                <button type="submit" class="btn btn-primary" form="bulkSearchForm">
                                    <i class="fas fa-search me-2"></i>
                                    Căutare în masă
                                </button>

                                <!-- Export Buttons for Mobile - Mobile Version -->
                                <?php if ($hasSearchCriteria && $totalResults > 0): ?>
                                    <div class="export-buttons-inline export-buttons-mobile">
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-success export-btn" id="csvExportBtnMobile" onclick="exportBulkResults('csv')">
                                                <i class="fas fa-file-csv me-1"></i>
                                                <span class="export-text">Export CSV</span>
                                                <span class="filter-indicator" style="display: none;">
                                                    <i class="fas fa-filter ms-1"></i>
                                                </span>
                                            </button>
                                            <button type="button" class="btn btn-info export-btn" id="excelExportBtnMobile" onclick="exportBulkResults('xlsx')">
                                                <i class="fas fa-file-excel me-1"></i>
                                                <span class="export-text">Export Excel</span>
                                                <span class="filter-indicator" style="display: none;">
                                                    <i class="fas fa-filter ms-1"></i>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if ($error): ?>
                    <!-- Error Display -->
                    <div class="alert alert-danger mt-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Eroare:</strong> <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php elseif ($hasSearchCriteria && !empty($searchResults)): ?>

                    <!-- Results Section -->
                    <div class="results-section">
                        <?php if (!empty($searchResults)): ?>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    Rezultate detaliate
                                </h5>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="expandAllResults()">
                                        <i class="fas fa-expand-alt me-1"></i>
                                        Expandează toate
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAllResults()">
                                        <i class="fas fa-compress-alt me-1"></i>
                                        Restrânge toate
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php foreach ($searchResults as $index => $result): ?>
                            <div class="term-results">
                                <div class="term-header" onclick="toggleTermResults(<?php echo $index; ?>)">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">
                                                <i class="fas fa-search me-2"></i>
                                                <?php echo htmlspecialchars($result['term']); ?>
                                                <?php
                                                // Show detected search type badge
                                                $searchTypeRo = '';
                                                $badgeClass = 'bg-secondary';
                                                $icon = 'fas fa-question';

                                                switch ($result['type'] ?? '') {
                                                    case 'numeParte':
                                                        $searchTypeRo = 'Nume parte';
                                                        $badgeClass = 'bg-primary';
                                                        $icon = 'fas fa-user';
                                                        break;
                                                    case 'numarDosar':
                                                        $searchTypeRo = 'Număr dosar';
                                                        $badgeClass = 'bg-success';
                                                        $icon = 'fas fa-folder';
                                                        break;
                                                    case 'obiectDosar':
                                                        $searchTypeRo = 'Obiect dosar';
                                                        $badgeClass = 'bg-info';
                                                        $icon = 'fas fa-gavel';
                                                        break;
                                                    default:
                                                        $searchTypeRo = 'Necunoscut';
                                                }
                                                ?>
                                                <span class="badge <?php echo $badgeClass; ?> ms-2" title="Tip detectat automat">
                                                    <i class="<?php echo $icon; ?> me-1"></i>
                                                    <?php echo $searchTypeRo; ?>
                                                </span>
                                            </h6>
                                            <small class="text-muted">
                                                <span id="resultMessage<?php echo $index; ?>" data-term="<?php echo htmlspecialchars($result['term']); ?>" data-original-count="<?php echo $result['count']; ?>">
                                                    <?php echo generateResultMessage($result['count'], $result['term']); ?>
                                                </span>
                                                <?php if (!empty($result['error'])): ?>
                                                    <span class="text-danger ms-2">
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                        Eroare: <?php echo htmlspecialchars($result['error']); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                        <div>
                                            <i class="fas fa-chevron-down toggle-icon" id="toggleIcon<?php echo $index; ?>"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="term-content" id="termContent<?php echo $index; ?>" style="display: none;">
                                    <?php if (!empty($result['results'])): ?>
                                        <!-- Desktop/Tablet Table View -->
                                        <div class="table-container table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th class="sortable-header" data-column="numar" data-term-index="<?php echo $index; ?>">
                                                            Număr Dosar
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="instanta" data-term-index="<?php echo $index; ?>">
                                                            Instanță
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="obiect" data-term-index="<?php echo $index; ?>">
                                                            Obiect
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="stadiu" data-term-index="<?php echo $index; ?>">
                                                            Stadiu Procesual
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="data" data-term-index="<?php echo $index; ?>">
                                                            Data
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="dataModificare" data-term-index="<?php echo $index; ?>">
                                                            Ultima Modificare
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="categorie" data-term-index="<?php echo $index; ?>">
                                                            Categorie caz
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="nume" data-term-index="<?php echo $index; ?>">
                                                            Nume Parte
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="calitate" data-term-index="<?php echo $index; ?>">
                                                            Calitate
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th>Acțiuni</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($result['results'] as $dosar): ?>
                                                        <?php
                                                        // Prepare data for sorting
                                                        $parti = $dosar->parti ?? [];
                                                        if (!is_array($parti)) {
                                                            $parti = [];
                                                        }
                                                        $searchTerm = $result['term'] ?? '';
                                                        $searchType = $result['type'] ?? '';
                                                        $relevantParty = getRelevantPartyName($parti, $searchTerm, $searchType);
                                                        $relevantQuality = getRelevantPartyQuality($parti, $searchTerm, $searchType);

                                                        // Get proper institution name
                                                        global $institutii;
                                                        if (!isset($institutii) || !is_array($institutii)) {
                                                            $institutii = getInstanteList();
                                                        }
                                                        $institutie = $dosar->institutie ?? '';
                                                        $instituteName = !empty($institutie) ? ($institutii[$institutie] ?? $institutie) : '';
                                                        ?>
                                                        <tr data-numar="<?php echo htmlspecialchars($dosar->numar ?? ''); ?>"
                                                            data-instanta="<?php echo htmlspecialchars($instituteName); ?>"
                                                            data-obiect="<?php echo htmlspecialchars($dosar->obiect ?? ''); ?>"
                                                            data-stadiu="<?php echo htmlspecialchars($dosar->stadiuProcesual ?? ''); ?>"
                                                            data-data="<?php echo htmlspecialchars($dosar->data ?? ''); ?>"
                                                            data-data-modificare="<?php echo htmlspecialchars($dosar->dataModificare ?? ''); ?>"
                                                            data-categorie="<?php echo htmlspecialchars($dosar->categorieCaz ?? ''); ?>"
                                                            data-nume="<?php echo htmlspecialchars($relevantParty); ?>"
                                                            data-calitate="<?php echo htmlspecialchars($relevantQuality ?: ''); ?>"
                                                            data-search-type="<?php echo htmlspecialchars($searchType); ?>">
                                                            <td>
                                                                <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar->numar ?? ''); ?>&institutie=<?php echo urlencode($dosar->institutie ?? ''); ?>"
                                                                   class="case-number-link"
                                                                   aria-label="Vezi detaliile dosarului <?php echo htmlspecialchars($dosar->numar ?? ''); ?>"
                                                                   title="Vezi detaliile dosarului"
                                                                   target="_blank">
                                                                    <strong><?php echo htmlspecialchars($dosar->numar ?? ''); ?></strong>
                                                                </a>
                                                            </td>
                                                            <td>
                                                                <?php echo htmlspecialchars($instituteName); ?>
                                                            </td>
                                                            <td>
                                                                <?php echo htmlspecialchars($dosar->obiect ?? ''); ?>
                                                            </td>
                                                            <td>
                                                                <?php echo htmlspecialchars($dosar->stadiuProcesual ?? ''); ?>
                                                            </td>
                                                            <td><?php echo htmlspecialchars($dosar->data ?? ''); ?></td>
                                                            <td>
                                                                <span class="text-muted">
                                                                    <?php echo htmlspecialchars($dosar->dataModificare ?? '-'); ?>
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <?php echo htmlspecialchars($dosar->categorieCaz ?? '-'); ?>
                                                            </td>
                                                            <td>
                                                                <?php
                                                                // FIXED: Ensure parti is an array for the functions
                                                                $parti = $dosar->parti ?? [];
                                                                if (!is_array($parti)) {
                                                                    $parti = [];
                                                                }



                                                                // Get search parameters
                                                                $searchTerm = $result['term'] ?? '';
                                                                $searchType = $result['type'] ?? '';

                                                                // NORDIS DEBUG: Log all NORDIS searches
                                                                if (stripos($searchTerm, 'NORDIS') !== false && $searchType === 'numeParte') {
                                                                    error_log("=== NORDIS SEARCH DEBUG ===");
                                                                    error_log("Search Term: '{$searchTerm}'");
                                                                    error_log("Case: " . ($dosar->numar ?? 'unknown'));
                                                                    error_log("Parties: " . count($parti));

                                                                    // Show all parties for this case
                                                                    foreach ($parti as $idx => $party) {
                                                                        $partyName = $party['nume'] ?? 'unknown';
                                                                        error_log("Party {$idx}: '{$partyName}'");
                                                                    }

                                                                    // Test smart matching
                                                                    $matchingParty = findMatchingParty($parti, $searchTerm);
                                                                    $matchingName = $matchingParty ? ($matchingParty['nume'] ?? 'unknown') : 'none';
                                                                    error_log("Matching party: '{$matchingName}'");
                                                                }

                                                                // Get relevant party name using enhanced matching
                                                                $relevantParty = getRelevantPartyName($parti, $searchTerm, $searchType);

                                                                if (stripos($searchTerm, 'NORDIS') !== false && $searchType === 'numeParte') {
                                                                    error_log("Relevant party: '{$relevantParty}'");

                                                                    // Test highlighting
                                                                    if (!empty($relevantParty)) {
                                                                        $highlighted = highlightMatchingPartyName($relevantParty, $searchTerm, $searchType);
                                                                        error_log("Highlighted result: '{$highlighted}'");
                                                                    }
                                                                    error_log("=== END NORDIS DEBUG ===");
                                                                }

                                                                // Display the result
                                                                if (!empty($relevantParty)) {
                                                                    echo highlightMatchingPartyName($relevantParty, $searchTerm, $searchType);
                                                                } else {
                                                                    echo '-';
                                                                }
                                                                ?>
                                                            </td>
                                                            <td>
                                                                <span class="badge bg-info text-dark">
                                                                    <?php
                                                                    // FIXED: Use the same parti array
                                                                    $relevantQuality = getRelevantPartyQuality($parti, $result['term'], $result['type']);
                                                                    echo htmlspecialchars($relevantQuality ?: '-');
                                                                    ?>
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar->numar ?? ''); ?>&institutie=<?php echo urlencode($dosar->institutie ?? ''); ?>"
                                                                   class="btn btn-sm btn-outline-primary"
                                                                   target="_blank">
                                                                    <i class="fas fa-eye me-1"></i>
                                                                    Detalii
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- Mobile Card View -->
                                        <div class="card-view">
                                            <?php foreach ($result['results'] as $dosar): ?>
                                            <div class="result-card"
                                                data-numar="<?php echo htmlspecialchars($dosar->numar ?? ''); ?>"
                                                data-instanta="<?php
                                                    // Get proper institution name using the mapping
                                                    $institutie = $dosar->institutie ?? '';
                                                    global $institutii;
                                                    if (!isset($institutii) || !is_array($institutii)) {
                                                        $institutii = getInstanteList();
                                                    }
                                                    $instituteName = !empty($institutie) ? ($institutii[$institutie] ?? $institutie) : '';
                                                    echo htmlspecialchars($instituteName);
                                                ?>"
                                                data-obiect="<?php echo htmlspecialchars($dosar->obiect ?? ''); ?>"
                                                data-stadiu="<?php echo htmlspecialchars($dosar->stadiuProcesual ?? ''); ?>"
                                                data-data="<?php echo htmlspecialchars($dosar->data ?? ''); ?>"
                                                data-categorieCaz="<?php echo htmlspecialchars($dosar->categorieCaz ?? ''); ?>"
                                                data-dataModificare="<?php echo htmlspecialchars($dosar->dataModificare ?? ''); ?>"
                                                data-nume="<?php
                                                    $parti = $dosar->parti ?? [];
                                                    if (!is_array($parti)) {
                                                        $parti = [];
                                                    }
                                                    $searchTerm = $result['term'] ?? '';
                                                    $searchType = $result['type'] ?? '';
                                                    $relevantParty = getRelevantPartyName($parti, $searchTerm, $searchType);
                                                    echo htmlspecialchars($relevantParty);
                                                ?>"
                                                data-calitate="<?php
                                                    $relevantQuality = getRelevantPartyQuality($parti, $result['term'], $result['type']);
                                                    echo htmlspecialchars($relevantQuality ?: '');
                                                ?>"
                                                data-search-type="<?php echo htmlspecialchars($result['type'] ?? ''); ?>">

                                                <div class="result-card-header">
                                                    <i class="fas fa-folder me-2"></i>
                                                    <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar->numar ?? ''); ?>&institutie=<?php echo urlencode($dosar->institutie ?? ''); ?>"
                                                       class="case-number-link"
                                                       aria-label="Vezi detaliile dosarului <?php echo htmlspecialchars($dosar->numar ?? ''); ?>"
                                                       title="Vezi detaliile dosarului"
                                                       target="_blank">
                                                        <?php echo htmlspecialchars($dosar->numar ?? ''); ?>
                                                    </a>
                                                </div>

                                                <div class="result-card-body">
                                                    <div class="result-card-item">
                                                        <div class="result-card-label">Instanță:</div>
                                                        <div class="result-card-value">
                                                            <?php
                                                            // Get proper institution name using the mapping
                                                            $institutie = $dosar->institutie ?? '';
                                                            global $institutii;
                                                            if (!isset($institutii) || !is_array($institutii)) {
                                                                $institutii = getInstanteList();
                                                            }
                                                            $instituteName = !empty($institutie) ? ($institutii[$institutie] ?? $institutie) : '';
                                                            echo htmlspecialchars($instituteName);
                                                            ?>
                                                        </div>
                                                    </div>

                                                    <div class="result-card-item">
                                                        <div class="result-card-label">Obiect:</div>
                                                        <div class="result-card-value">
                                                            <?php echo htmlspecialchars($dosar->obiect ?? ''); ?>
                                                        </div>
                                                    </div>

                                                    <div class="result-card-item">
                                                        <div class="result-card-label">Stadiu procesual:</div>
                                                        <div class="result-card-value">
                                                            <?php echo htmlspecialchars($dosar->stadiuProcesual ?? ''); ?>
                                                        </div>
                                                    </div>

                                                    <div class="result-card-item">
                                                        <div class="result-card-label">Data:</div>
                                                        <div class="result-card-value">
                                                            <?php echo htmlspecialchars($dosar->data ?? ''); ?>
                                                        </div>
                                                    </div>

                                                    <div class="result-card-item">
                                                        <div class="result-card-label">Categorie caz:</div>
                                                        <div class="result-card-value">
                                                            <?php echo htmlspecialchars($dosar->categorieCaz ?? '-'); ?>
                                                        </div>
                                                    </div>

                                                    <div class="result-card-item">
                                                        <div class="result-card-label">Data ultimei modificări:</div>
                                                        <div class="result-card-value">
                                                            <?php echo htmlspecialchars($dosar->dataModificare ?? '-'); ?>
                                                        </div>
                                                    </div>

                                                    <div class="result-card-item">
                                                        <div class="result-card-label">Nume parte:</div>
                                                        <div class="result-card-value">
                                                            <?php
                                                            $parti = $dosar->parti ?? [];
                                                            if (!is_array($parti)) {
                                                                $parti = [];
                                                            }
                                                            $searchTerm = $result['term'] ?? '';
                                                            $searchType = $result['type'] ?? '';
                                                            $relevantParty = getRelevantPartyName($parti, $searchTerm, $searchType);

                                                            if (!empty($relevantParty)) {
                                                                echo highlightMatchingPartyName($relevantParty, $searchTerm, $searchType);
                                                                // Show indicator for multiple parties
                                                                if (!empty($parti) && count($parti) > 1) {
                                                                    echo ' <small class="text-muted">(+' . (count($parti) - 1) . ' alții)</small>';
                                                                }
                                                            } else {
                                                                echo '-';
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>

                                                    <div class="result-card-item">
                                                        <div class="result-card-label">Calitate:</div>
                                                        <div class="result-card-value">
                                                            <span class="badge bg-info text-dark">
                                                                <?php
                                                                $relevantQuality = getRelevantPartyQuality($parti, $result['term'], $result['type']);
                                                                echo htmlspecialchars($relevantQuality ?: '-');
                                                                ?>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="result-card-actions">
                                                    <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar->numar ?? ''); ?>&institutie=<?php echo urlencode($dosar->institutie ?? ''); ?>"
                                                       class="btn btn-sm btn-outline-primary"
                                                       target="_blank">
                                                        <i class="fas fa-eye me-1"></i>
                                                        Detalii
                                                    </a>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                            <h6 class="text-muted">Nu au fost găsite rezultate pentru acest termen</h6>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php elseif ($hasSearchCriteria && empty($searchResults)): ?>
                    <!-- No Results -->
                    <div class="alert alert-info mt-4">
                        <i class="fas fa-info-circle me-2"></i>
                        Nu au fost găsite rezultate pentru termenii de căutare specificați.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <script>
        // Global variables
        let currentSearchTerms = '';

        // Remove duplicate DOMContentLoaded - functionality moved to the enhanced version below

        /**
         * Initialize term counter functionality
         */
        function initTermCounter() {
            const textarea = document.getElementById('bulkSearchTerms');
            const termCount = document.getElementById('termCount');
            const termLimit = document.getElementById('termLimit');

            function updateTermCount() {
                const terms = parseBulkTerms(textarea.value);
                const count = terms.length;

                termCount.textContent = count;

                // Update styling based on count
                if (count > 100) {
                    termCount.className = 'text-danger fw-bold';
                    termLimit.className = 'text-danger';
                } else if (count > 80) {
                    termCount.className = 'text-warning fw-bold';
                    termLimit.className = 'text-warning';
                } else {
                    termCount.className = 'text-primary fw-bold';
                    termLimit.className = 'text-muted';
                }
            }

            textarea.addEventListener('input', updateTermCount);
            updateTermCount(); // Initial count
        }

        /**
         * Parse bulk search terms from textarea
         */
        function parseBulkTerms(input) {
            if (!input.trim()) return [];

            // Replace commas with newlines for uniform processing
            input = input.replace(/,/g, '\n');

            // Split by lines and clean
            const terms = input.split('\n')
                .map(term => term.trim())
                .filter(term => term.length >= 2);

            // Remove duplicates
            return [...new Set(terms)];
        }

        /**
         * Initialize form validation
         */
        function initFormValidation() {
            const form = document.getElementById('bulkSearchForm');
            const textarea = document.getElementById('bulkSearchTerms');

            form.addEventListener('submit', function(e) {
                const terms = parseBulkTerms(textarea.value);

                // Check if we have any advanced filters
                const hasAdvancedFilters = checkAdvancedFilters();

                // Require either search terms OR advanced filters
                if (terms.length === 0 && !hasAdvancedFilters) {
                    e.preventDefault();
                    showNotification('Vă rugăm să introduceți termeni de căutare SAU să selectați cel puțin un filtru avansat.', 'warning');
                    return false;
                }

                if (terms.length > 100) {
                    e.preventDefault();
                    showNotification(`Numărul maxim de termeni este 100. Ați introdus ${terms.length} termeni.`, 'danger');
                    return false;
                }

                // Show loading overlay
                showLoadingOverlay();

                // Update search parameters for export
                currentSearchTerms = textarea.value;

                return true;
            });
        }

        /**
         * Check if any advanced filters are selected
         */
        function checkAdvancedFilters() {
            const institutie = document.getElementById('institutie');
            const categorieInstanta = document.getElementById('categorieInstanta');
            const categorieCaz = document.getElementById('categorieCaz');
            const dataInceput = document.getElementById('dataInceput');
            const dataSfarsit = document.getElementById('dataSfarsit');

            return (institutie && institutie.value) ||
                   (categorieInstanta && categorieInstanta.value) ||
                   (categorieCaz && categorieCaz.value) ||
                   (dataInceput && dataInceput.value.trim()) ||
                   (dataSfarsit && dataSfarsit.value.trim());
        }

        /**
         * Show loading overlay with progress simulation
         */
        function showLoadingOverlay() {
            const overlay = document.getElementById('loadingOverlay');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const loadingMessage = document.getElementById('loadingMessage');
            const loadingSubmessage = document.getElementById('loadingSubmessage');

            overlay.style.display = 'flex';

            // Simulate progress
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;

                progressBar.style.width = progress + '%';
                progressText.textContent = Math.round(progress) + '%';

                // Update messages based on progress
                if (progress < 30) {
                    loadingMessage.textContent = 'Se inițializează căutarea în masă...';
                    loadingSubmessage.textContent = 'Se pregătesc parametrii de căutare...';
                } else if (progress < 60) {
                    loadingMessage.textContent = 'Se procesează termenii de căutare...';
                    loadingSubmessage.textContent = 'Se efectuează apelurile către API...';
                } else {
                    loadingMessage.textContent = 'Se finalizează procesarea...';
                    loadingSubmessage.textContent = 'Se organizează rezultatele...';
                }
            }, 200);

            // Clear interval after form submission
            setTimeout(() => {
                clearInterval(interval);
            }, 10000);
        }

        /**
         * Toggle term results visibility
         */
        function toggleTermResults(index) {
            const content = document.getElementById('termContent' + index);
            const icon = document.getElementById('toggleIcon' + index);

            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.className = 'fas fa-chevron-up toggle-icon';
            } else {
                content.style.display = 'none';
                icon.className = 'fas fa-chevron-down toggle-icon';
            }
        }

        /**
         * Export bulk search results
         * ENHANCED: Respects exact match filter state
         */
        function exportBulkResults(format) {
            // Check if we have search terms OR advanced filters
            const hasAdvancedFilters = checkAdvancedFilters();

            if (!currentSearchTerms.trim() && !hasAdvancedFilters) {
                showNotification('Nu există termeni de căutare sau filtre pentru export.', 'warning');
                return;
            }

            // Check exact match filter state
            const exactMatchFilter = document.getElementById('exactMatchFilter');
            const isFilterActive = exactMatchFilter && exactMatchFilter.checked;

            // Enhanced loading notification based on filter state
            if (isFilterActive) {
                showNotification(`Se pregătește exportul ${format.toUpperCase()} cu filtrul de potriviri exacte aplicat...`, 'info');
            } else {
                showNotification(`Se pregătește exportul ${format.toUpperCase()}...`, 'info');
            }

            // Build export URL with filter state and advanced filters
            const params = new URLSearchParams({
                'export': format,
                'bulk_results': '1',
                'bulkSearchTerms': currentSearchTerms || ''
            });

            // Add exact match filter parameter if active
            if (isFilterActive) {
                params.set('exactMatchOnly', 'true');
                console.log('Export with exact match filter enabled');
            }

            // Add advanced filter parameters
            const institutieSelect = document.getElementById('institutie');
            const categorieInstantaSelect = document.getElementById('categorieInstanta');
            const categorieCazSelect = document.getElementById('categorieCaz');
            const dataInceputInput = document.getElementById('dataInceput');
            const dataSfarsitInput = document.getElementById('dataSfarsit');

            if (institutieSelect && institutieSelect.value) {
                params.set('institutie', institutieSelect.value);
                console.log('Export with institution filter:', institutieSelect.value);
            }

            if (categorieInstantaSelect && categorieInstantaSelect.value) {
                params.set('categorieInstanta', categorieInstantaSelect.value);
                console.log('Export with institution category filter:', categorieInstantaSelect.value);
            }

            if (categorieCazSelect && categorieCazSelect.value) {
                params.set('categorieCaz', categorieCazSelect.value);
                console.log('Export with case category filter:', categorieCazSelect.value);
            }

            if (dataInceputInput && dataInceputInput.value) {
                params.set('dataInceput', dataInceputInput.value);
                console.log('Export with start date filter:', dataInceputInput.value);
            }

            if (dataSfarsitInput && dataSfarsitInput.value) {
                params.set('dataSfarsit', dataSfarsitInput.value);
                console.log('Export with end date filter:', dataSfarsitInput.value);
            }

            // Create download link
            const downloadUrl = 'avans.php?' + params.toString();

            // Trigger download
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Enhanced success message based on filter state
            setTimeout(() => {
                if (isFilterActive) {
                    showNotification(`Fișierul ${format.toUpperCase()} cu potriviri exacte a fost descărcat cu succes!`, 'success');
                } else {
                    showNotification(`Fișierul ${format.toUpperCase()} a fost descărcat cu succes!`, 'success');
                }
            }, 2000);
        }

        /**
         * Initialize notification system
         */
        function initNotificationSystem() {
            // Create notification container if it doesn't exist
            if (!document.getElementById('notificationContainer')) {
                const container = document.createElement('div');
                container.id = 'notificationContainer';
                container.className = 'notification-container';
                container.style.display = 'none';

                const notification = document.createElement('div');
                notification.id = 'notification';
                notification.className = 'alert';
                notification.setAttribute('role', 'alert');

                container.appendChild(notification);
                document.body.appendChild(container);
            }
        }

        /**
         * Show notification message
         */
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.getElementById('notification');

            if (!container || !notification) {
                console.error('Notification elements not found');
                return;
            }

            // Icon mapping
            const iconMap = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };

            const icon = iconMap[type] || 'info-circle';

            // Set notification content and style
            notification.className = `alert alert-${type}`;
            notification.innerHTML = `<i class="fas fa-${icon} me-2"></i>${message}`;

            // Show notification
            container.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                container.style.display = 'none';
            }, 5000);
        }

        // Expand all results function
        function expandAllResults() {
            const termContents = document.querySelectorAll('[id^="termContent"]');
            const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

            termContents.forEach(content => {
                content.style.display = 'block';
            });

            toggleIcons.forEach(icon => {
                icon.className = 'fas fa-chevron-up toggle-icon';
            });

            showNotification('Toate secțiunile au fost expandate.', 'info');
        }

        // Collapse all results function
        function collapseAllResults() {
            const termContents = document.querySelectorAll('[id^="termContent"]');
            const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

            termContents.forEach(content => {
                content.style.display = 'none';
            });

            toggleIcons.forEach(icon => {
                icon.className = 'fas fa-chevron-down toggle-icon';
            });

            showNotification('Toate secțiunile au fost restrânse.', 'info');
        }

        /**
         * Table Sorting Functionality
         */

        // Store sort state for each table
        const tableSortState = {};

        /**
         * Initialize sortable table functionality
         */
        function initSortableTables() {
            // Add click listeners to all sortable headers
            document.querySelectorAll('.sortable-header').forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-column');
                    const termIndex = this.getAttribute('data-term-index');
                    const tableId = `table-${termIndex}`;

                    sortTable(tableId, column, termIndex);
                });
            });
        }

        /**
         * Sort table by column
         */
        function sortTable(tableId, column, termIndex) {
            const table = document.querySelector(`#termContent${termIndex} .table-responsive table`);
            if (!table) return;

            const tbody = table.querySelector('tbody');
            if (!tbody) return;

            // Initialize sort state for this table if not exists
            if (!tableSortState[tableId]) {
                tableSortState[tableId] = { column: null, direction: 'asc' };
            }

            const currentState = tableSortState[tableId];

            // Determine new sort direction
            let newDirection = 'asc';
            if (currentState.column === column) {
                newDirection = currentState.direction === 'asc' ? 'desc' : 'asc';
            }

            // Update sort state
            currentState.column = column;
            currentState.direction = newDirection;

            // Update header indicators
            updateSortIndicators(termIndex, column, newDirection);

            // Get all rows
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Sort rows
            rows.sort((a, b) => {
                const aValue = getSortValue(a, column);
                const bValue = getSortValue(b, column);

                const comparison = compareSortValues(aValue, bValue, column);
                return newDirection === 'asc' ? comparison : -comparison;
            });

            // Clear tbody and append sorted rows
            tbody.innerHTML = '';
            rows.forEach(row => tbody.appendChild(row));

            // Show notification
            const columnNames = {
                'numar': 'Număr Dosar',
                'instanta': 'Instanță',
                'obiect': 'Obiect',
                'stadiu': 'Stadiu Procesual',
                'data': 'Data',
                'dataModificare': 'Ultima Modificare',
                'categorie': 'Categorie caz',
                'nume': 'Nume Parte',
                'calitate': 'Calitate'
            };

            const directionText = newDirection === 'asc' ? 'crescător' : 'descrescător';
            showNotification(`Tabelul a fost sortat după ${columnNames[column]} în ordine ${directionText}.`, 'info');
        }

        /**
         * Update sort indicators in table headers
         */
        function updateSortIndicators(termIndex, activeColumn, direction) {
            // Reset all indicators for this table
            document.querySelectorAll(`[data-term-index="${termIndex}"] .sort-indicator`).forEach(indicator => {
                indicator.className = 'sort-indicator sort-none';
            });

            // Update active column indicator
            const activeHeader = document.querySelector(`[data-term-index="${termIndex}"][data-column="${activeColumn}"] .sort-indicator`);
            if (activeHeader) {
                activeHeader.className = `sort-indicator sort-${direction}`;
            }

            // Update header classes
            document.querySelectorAll(`[data-term-index="${termIndex}"].sortable-header`).forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
                if (header.getAttribute('data-column') === activeColumn) {
                    header.classList.add(`sort-${direction}`);
                }
            });
        }

        /**
         * Get sort value from table row
         */
        function getSortValue(row, column) {
            const value = row.getAttribute(`data-${column}`) || '';
            return value.trim();
        }

        /**
         * Compare sort values with proper handling for different data types
         */
        function compareSortValues(a, b, column) {
            // Handle empty values
            if (!a && !b) return 0;
            if (!a) return 1;
            if (!b) return -1;

            // Date columns - handle Romanian DD.MM.YYYY format
            if (column === 'data' || column === 'dataModificare') {
                return compareDates(a, b);
            }

            // Numeric columns (case numbers)
            if (column === 'numar') {
                return compareNumbers(a, b);
            }

            // Text columns - handle Romanian diacritics
            return compareText(a, b);
        }

        /**
         * Compare Romanian dates in DD.MM.YYYY format
         */
        function compareDates(dateA, dateB) {
            const parseRomanianDate = (dateStr) => {
                if (!dateStr || dateStr === '-') return new Date(0);

                // Handle DD.MM.YYYY format
                const parts = dateStr.split('.');
                if (parts.length === 3) {
                    const day = parseInt(parts[0], 10);
                    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
                    const year = parseInt(parts[2], 10);
                    return new Date(year, month, day);
                }

                // Fallback to standard parsing
                return new Date(dateStr);
            };

            const dateObjA = parseRomanianDate(dateA);
            const dateObjB = parseRomanianDate(dateB);

            return dateObjA.getTime() - dateObjB.getTime();
        }

        /**
         * Compare numbers (case numbers like 1234/2023)
         */
        function compareNumbers(numA, numB) {
            // Extract first number from case number format
            const extractNumber = (str) => {
                const match = str.match(/^(\d+)/);
                return match ? parseInt(match[1], 10) : 0;
            };

            const numberA = extractNumber(numA);
            const numberB = extractNumber(numB);

            if (numberA !== numberB) {
                return numberA - numberB;
            }

            // If first numbers are equal, compare as strings
            return numA.localeCompare(numB, 'ro', { numeric: true });
        }

        /**
         * Compare text with Romanian diacritics support
         */
        function compareText(textA, textB) {
            // Normalize Romanian diacritics for comparison
            const normalizeRomanian = (text) => {
                return text.toLowerCase()
                    .replace(/ă/g, 'a')
                    .replace(/â/g, 'a')
                    .replace(/î/g, 'i')
                    .replace(/ș/g, 's')
                    .replace(/ț/g, 't');
            };

            const normalizedA = normalizeRomanian(textA);
            const normalizedB = normalizeRomanian(textB);

            return normalizedA.localeCompare(normalizedB, 'ro', {
                numeric: true,
                sensitivity: 'base'
            });
        }

        /**
         * Generate result message in Romanian
         */
        function generateResultMessage(count, term) {
            if (count === 0) {
                return `Nu au fost găsite rezultate pentru termenul '${term}'`;
            } else if (count === 1) {
                return `1 rezultat găsit pentru termenul '${term}'`;
            } else {
                return `${count} rezultate găsite pentru termenul '${term}'`;
            }
        }

        /**
         * Update result counters for each term based on filter state
         */
        function updateResultCounters(isFilterActive, termCounts = {}) {
            const resultMessages = document.querySelectorAll('[id^="resultMessage"]');
            let totalFilteredResults = 0;

            resultMessages.forEach(messageElement => {
                const term = messageElement.getAttribute('data-term');
                const originalCount = parseInt(messageElement.getAttribute('data-original-count'));

                if (isFilterActive) {
                    // Use the term-specific count from the filter results
                    const termIndex = messageElement.id.replace('resultMessage', '');
                    const filteredCount = termCounts[termIndex] || 0;

                    // Update the message with filtered count
                    messageElement.textContent = generateResultMessage(filteredCount, term);
                    totalFilteredResults += filteredCount;
                } else {
                    // Restore original count
                    messageElement.textContent = generateResultMessage(originalCount, term);
                    totalFilteredResults += originalCount;
                }
            });

            // Update total results counter
            updateTotalResultsCounter(isFilterActive ? totalFilteredResults : null);
        }

        /**
         * Update the total results counter in the search form
         */
        function updateTotalResultsCounter(filteredTotal = null) {
            const totalCounter = document.getElementById('totalResultsCounter');

            if (totalCounter) {
                if (filteredTotal !== null) {
                    // Show filtered total
                    totalCounter.textContent = filteredTotal;
                } else {
                    // Restore original total
                    const originalTotal = parseInt(totalCounter.getAttribute('data-original-total'));
                    totalCounter.textContent = originalTotal;
                }
            }
        }

        /**
         * Toggle exact match filter
         * ENHANCED: Better error handling and debugging + counter updates
         */
        function toggleExactMatchFilter() {
            const checkbox = document.getElementById('exactMatchFilter');
            const filterStatus = document.getElementById('filterStatus');
            const filteredCount = document.getElementById('filteredCount');

            if (!checkbox || !filterStatus || !filteredCount) {
                console.error('Filter elements not found');
                showNotification('Eroare: Elementele filtrului nu au fost găsite.', 'danger');
                return;
            }

            console.log('Filter toggle triggered, checkbox checked:', checkbox.checked);

            if (checkbox.checked) {
                // Apply exact match filter
                const filteredResults = applyExactMatchFilter();

                // Update result counters to show filtered counts
                updateResultCounters(true, filteredResults.termCounts);

                // Show filter status
                filterStatus.style.display = 'block';
                filteredCount.textContent = filteredResults.visible;

                // Update export button indicators
                updateExportButtonIndicators(true);

                // Enhanced notification with more details
                if (filteredResults.visible === 0) {
                    showNotification('Nu au fost găsite rezultate cu potriviri exacte sau căutări după număr dosar. Verificați termenii de căutare.', 'warning');
                } else {
                    showNotification(`Filtrul a fost aplicat. Se afișează ${filteredResults.visible} din ${filteredResults.total} rezultate (potriviri exacte pentru nume și toate căutările după număr dosar).`, 'success');
                }

                // Store filter state
                sessionStorage.setItem('exactMatchFilter', 'true');
                console.log('Filter applied and state saved');
            } else {
                // Remove filter
                removeExactMatchFilter();

                // Update result counters to show original counts
                updateResultCounters(false);

                // Hide filter status
                filterStatus.style.display = 'none';

                // Update export button indicators
                updateExportButtonIndicators(false);

                // Show notification
                showNotification('Filtrul a fost eliminat. Se afișează toate rezultatele pentru toate tipurile de căutare.', 'info');

                // Clear filter state
                sessionStorage.removeItem('exactMatchFilter');
                console.log('Filter removed and state cleared');
            }

            // Preserve sort state after filtering
            preserveSortStateAfterFilter();
        }

        /**
         * Apply exact match filter to results
         * ENHANCED: Now detects both exact-match and exact-phrase-match classes + counts per term
         */
        function applyExactMatchFilter() {
            const allRows = document.querySelectorAll('.table tbody tr');
            let totalRows = 0;
            let visibleRows = 0;

            // Track counts per term section
            const termCounts = {};

            allRows.forEach(row => {
                totalRows++;

                // Find which term section this row belongs to
                const termContent = row.closest('[id^="termContent"]');
                let termIndex = null;
                if (termContent) {
                    termIndex = termContent.id.replace('termContent', '');
                }

                // Check if row contains exact match (exact-match, exact-phrase-match, or partial-match for quoted searches)
                const partyCell = row.querySelector('td:nth-child(8)'); // Nume Parte column
                if (partyCell) {
                    // Look for all types of exact matches (including partial matches from quoted searches)
                    const hasExactMatch = partyCell.querySelector('.matching-party-name.exact-match') ||
                                        partyCell.querySelector('.matching-party-name.exact-phrase-match') ||
                                        partyCell.querySelector('.matching-party-name.partial-match');

                    if (hasExactMatch) {
                        row.style.display = '';
                        row.classList.add('filtered-exact-match'); // Add class for debugging
                        visibleRows++;

                        // Count for this term
                        if (termIndex !== null) {
                            termCounts[termIndex] = (termCounts[termIndex] || 0) + 1;
                        }
                    } else {
                        row.style.display = 'none';
                        row.classList.remove('filtered-exact-match');
                    }
                } else {
                    // If no party cell found, hide the row
                    row.style.display = 'none';
                    row.classList.remove('filtered-exact-match');
                }
            });

            // Debug logging
            console.log(`Filter applied: ${visibleRows} exact matches found out of ${totalRows} total rows`);
            console.log('Term counts:', termCounts);

            return {
                total: totalRows,
                visible: visibleRows,
                termCounts: termCounts
            };
        }

        /**
         * Remove exact match filter
         * ENHANCED: Clean up debugging classes
         */
        function removeExactMatchFilter() {
            const allRows = document.querySelectorAll('.table tbody tr');

            allRows.forEach(row => {
                row.style.display = '';
                row.classList.remove('filtered-exact-match');
            });

            console.log('Filter removed: All rows are now visible');
        }

        /**
         * Update export button indicators based on filter state
         * Handles both desktop and mobile export buttons
         */
        function updateExportButtonIndicators(isFilterActive) {
            // Desktop export buttons
            const csvBtn = document.getElementById('csvExportBtn');
            const excelBtn = document.getElementById('excelExportBtn');

            // Mobile export buttons
            const csvBtnMobile = document.getElementById('csvExportBtnMobile');
            const excelBtnMobile = document.getElementById('excelExportBtnMobile');

            // Array of all export buttons
            const allButtons = [csvBtn, excelBtn, csvBtnMobile, excelBtnMobile].filter(btn => btn !== null);

            if (allButtons.length > 0) {
                if (isFilterActive) {
                    allButtons.forEach(btn => {
                        btn.classList.add('filter-active');

                        // Show filter indicator
                        const indicator = btn.querySelector('.filter-indicator');
                        if (indicator) indicator.style.display = 'inline';
                    });

                    console.log('Export button indicators activated for all buttons');
                } else {
                    allButtons.forEach(btn => {
                        btn.classList.remove('filter-active');

                        // Hide filter indicator
                        const indicator = btn.querySelector('.filter-indicator');
                        if (indicator) indicator.style.display = 'none';
                    });

                    console.log('Export button indicators deactivated for all buttons');
                }
            }
        }

        /**
         * Restore filter state on page load
         */
        function restoreFilterState() {
            const filterState = sessionStorage.getItem('exactMatchFilter');
            const checkbox = document.getElementById('exactMatchFilter');

            if (filterState === 'true' && checkbox) {
                checkbox.checked = true;
                toggleExactMatchFilter();
            }
        }

        /**
         * Apply enhanced filter to mobile card view - includes case number searches
         */
        function applyExactMatchFilterToCards() {
            const allCards = document.querySelectorAll('.result-card');
            let totalCards = 0;
            let visibleCards = 0;

            allCards.forEach(card => {
                totalCards++;

                // Get the search type for this card
                const searchType = card.getAttribute('data-search-type') || '';

                let shouldShowCard = false;

                // Always include case number searches (they are inherently exact)
                if (searchType === 'numarDosar') {
                    shouldShowCard = true;
                }
                // For party name searches, check for exact matches
                else if (searchType === 'numeParte') {
                    const partyValue = card.querySelector('.result-card-item:nth-child(7) .result-card-value'); // Nume Parte
                    if (partyValue) {
                        // Look for all types of exact matches
                        const hasExactMatch = partyValue.querySelector('.matching-party-name.exact-match') ||
                                            partyValue.querySelector('.matching-party-name.exact-phrase-match') ||
                                            partyValue.querySelector('.matching-party-name.partial-match');

                        if (hasExactMatch) {
                            shouldShowCard = true;
                        }
                    }
                }
                // Optionally include case object searches as well
                else if (searchType === 'obiectDosar') {
                    shouldShowCard = true;
                }

                if (shouldShowCard) {
                    card.style.display = '';
                    card.classList.add('filtered-exact-match');
                    visibleCards++;
                } else {
                    card.style.display = 'none';
                    card.classList.remove('filtered-exact-match');
                }
            });

            console.log(`Enhanced mobile card filter applied: ${visibleCards} results shown out of ${totalCards} total cards`);
            console.log('Included: exact party matches, all case number searches, and case object searches');
            return { total: totalCards, visible: visibleCards };
        }

        /**
         * Enhanced exact match filter that works for both table and card views
         */
        function applyExactMatchFilter() {
            // Apply to table view
            const tableResults = applyExactMatchFilterToTable();

            // Apply to card view
            const cardResults = applyExactMatchFilterToCards();

            // Return combined results (table results are primary)
            return tableResults;
        }

        /**
         * Enhanced table filtering function - includes case number searches
         */
        function applyExactMatchFilterToTable() {
            const allRows = document.querySelectorAll('.table tbody tr');
            let totalRows = 0;
            let visibleRows = 0;

            // Track counts per term section
            const termCounts = {};

            allRows.forEach(row => {
                totalRows++;

                // Find which term section this row belongs to
                const termContent = row.closest('[id^="termContent"]');
                let termIndex = null;
                if (termContent) {
                    termIndex = termContent.id.replace('termContent', '');
                }

                // Get the search type for this row
                const searchType = row.getAttribute('data-search-type') || '';

                let shouldShowRow = false;

                // Always include case number searches (they are inherently exact)
                if (searchType === 'numarDosar') {
                    shouldShowRow = true;
                    console.log('Including case number search result:', row.getAttribute('data-numar'));
                }
                // For party name searches, check for exact matches
                else if (searchType === 'numeParte') {
                    const partyCell = row.querySelector('td:nth-child(8)'); // Nume Parte column
                    if (partyCell) {
                        // Look for all types of exact matches (including partial matches from quoted searches)
                        const hasExactMatch = partyCell.querySelector('.matching-party-name.exact-match') ||
                                            partyCell.querySelector('.matching-party-name.exact-phrase-match') ||
                                            partyCell.querySelector('.matching-party-name.partial-match');

                        if (hasExactMatch) {
                            shouldShowRow = true;
                            console.log('Including exact party name match:', row.getAttribute('data-nume'));
                        }
                    }
                }
                // Optionally include case object searches as well
                else if (searchType === 'obiectDosar') {
                    shouldShowRow = true;
                    console.log('Including case object search result:', row.getAttribute('data-obiect'));
                }

                if (shouldShowRow) {
                    row.style.display = '';
                    row.classList.add('filtered-exact-match'); // Add class for debugging
                    visibleRows++;

                    // Count for this term
                    if (termIndex !== null) {
                        termCounts[termIndex] = (termCounts[termIndex] || 0) + 1;
                    }
                } else {
                    row.style.display = 'none';
                    row.classList.remove('filtered-exact-match');
                }
            });

            // Debug logging
            console.log(`Enhanced filter applied: ${visibleRows} results shown out of ${totalRows} total rows`);
            console.log('Included: exact party matches, all case number searches, and case object searches');
            console.log('Term counts:', termCounts);

            return {
                total: totalRows,
                visible: visibleRows,
                termCounts: termCounts
            };
        }

        /**
         * Remove exact match filter from results
         */
        function removeExactMatchFilter() {
            const allRows = document.querySelectorAll('.table tbody tr');

            allRows.forEach(row => {
                row.style.display = '';
                row.classList.remove('filtered-exact-match');
            });

            // Also remove filter from mobile card view
            const allCards = document.querySelectorAll('.result-card');
            allCards.forEach(card => {
                card.style.display = '';
                card.classList.remove('filtered-exact-match');
            });

            console.log('Filter removed from all rows and cards');
        }

        /**
         * Preserve sort state after filtering
         */
        function preserveSortStateAfterFilter() {
            // Re-apply current sort to visible rows after a short delay
            setTimeout(() => {
                Object.keys(tableSortState).forEach(tableId => {
                    const state = tableSortState[tableId];
                    if (state.column) {
                        const termIndex = tableId.replace('table-', '');
                        // Re-sort the table with current settings
                        sortTable(tableId, state.column, termIndex);
                    }
                });
            }, 100);
        }

        /**
         * Validează data în format românesc DD.MM.YYYY
         */
        function validateRomanianDateJS(dateString) {
            if (!dateString) return { valid: true, error: '' };

            const pattern = /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/;
            const match = dateString.match(pattern);

            if (!match) {
                return { valid: false, error: 'Formatul datei trebuie să fie ZZ.LL.AAAA (ex: 15.03.2023)' };
            }

            const day = parseInt(match[1], 10);
            const month = parseInt(match[2], 10);
            const year = parseInt(match[3], 10);

            // Verificăm validitatea datei
            const date = new Date(year, month - 1, day);
            if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
                return { valid: false, error: 'Data introdusă nu este validă' };
            }

            // Verificăm limitele anului
            const currentYear = new Date().getFullYear();
            if (year < 1990 || year > currentYear + 5) {
                return { valid: false, error: `Anul trebuie să fie între 1990 și ${currentYear + 5}` };
            }

            return { valid: true, error: '' };
        }

        /**
         * Inițializează funcționalitatea de toggle pentru filtrele avansate
         */
        function initAdvancedFiltersToggle() {
            const toggleButton = document.getElementById('advancedFiltersToggle');
            const filtersSection = document.getElementById('advancedFilters');
            const toggleText = toggleButton?.querySelector('span');
            const chevronIcon = toggleButton?.querySelector('i.fa-chevron-down');

            if (!toggleButton || !filtersSection || !toggleText) {
                console.warn('Advanced filters toggle elements not found');
                return;
            }

            // Function to show filters
            function showFilters() {
                filtersSection.classList.add('show');
                toggleText.textContent = 'Ascunde filtrele avansate';
                toggleButton.classList.add('expanded');
                toggleButton.setAttribute('aria-expanded', 'true');
            }

            // Function to hide filters
            function hideFilters() {
                filtersSection.classList.remove('show');
                toggleText.textContent = 'Arată filtrele avansate';
                toggleButton.classList.remove('expanded');
                toggleButton.setAttribute('aria-expanded', 'false');
            }

            // Toggle click handler
            toggleButton.addEventListener('click', function(e) {
                e.preventDefault();

                const isVisible = filtersSection.classList.contains('show');

                if (isVisible) {
                    hideFilters();
                } else {
                    showFilters();
                }
            });

            // Keyboard accessibility
            toggleButton.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });

            // Check if any filters are already selected and show the section
            const hasSelectedFilters = checkAdvancedFilters();
            if (hasSelectedFilters) {
                showFilters();
            } else {
                hideFilters();
            }

            // Set initial ARIA attributes
            toggleButton.setAttribute('aria-controls', 'advancedFilters');
            toggleButton.setAttribute('role', 'button');
        }

        /**
         * Inițializează validarea datelor
         */
        function initDateValidation() {
            const startDateInput = document.getElementById('dataInceput');
            const endDateInput = document.getElementById('dataSfarsit');

            if (!startDateInput || !endDateInput) return;

            function validateDateInput(input) {
                const value = input.value.trim();
                const validation = validateRomanianDateJS(value);

                input.classList.remove('is-valid', 'is-invalid');

                if (value && !validation.valid) {
                    input.classList.add('is-invalid');
                    showDateError(input, validation.error);
                } else if (value && validation.valid) {
                    input.classList.add('is-valid');
                    hideDateError(input);
                } else {
                    hideDateError(input);
                }

                // Validăm intervalul de date
                validateDateRange();
            }

            function validateDateRange() {
                const startValue = startDateInput.value.trim();
                const endValue = endDateInput.value.trim();

                if (!startValue || !endValue) return;

                const startValidation = validateRomanianDateJS(startValue);
                const endValidation = validateRomanianDateJS(endValue);

                if (startValidation.valid && endValidation.valid) {
                    const startParts = startValue.split('.');
                    const endParts = endValue.split('.');
                    const startDate = new Date(startParts[2], startParts[1] - 1, startParts[0]);
                    const endDate = new Date(endParts[2], endParts[1] - 1, endParts[0]);

                    if (startDate > endDate) {
                        endDateInput.classList.remove('is-valid');
                        endDateInput.classList.add('is-invalid');
                        showDateError(endDateInput, 'Data sfârșit trebuie să fie după data început');
                    }
                }
            }

            function showDateError(input, message) {
                hideDateError(input);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = message;
                errorDiv.id = input.id + '_error';
                input.parentNode.appendChild(errorDiv);
            }

            function hideDateError(input) {
                const existingError = document.getElementById(input.id + '_error');
                if (existingError) {
                    existingError.remove();
                }
            }

            // Adăugăm event listeners
            startDateInput.addEventListener('blur', () => validateDateInput(startDateInput));
            endDateInput.addEventListener('blur', () => validateDateInput(endDateInput));
            startDateInput.addEventListener('input', () => {
                // Formatare automată în timpul tastării
                let value = startDateInput.value.replace(/[^\d]/g, '');
                if (value.length >= 2) value = value.substring(0, 2) + '.' + value.substring(2);
                if (value.length >= 5) value = value.substring(0, 5) + '.' + value.substring(5, 9);
                startDateInput.value = value;
            });
            endDateInput.addEventListener('input', () => {
                // Formatare automată în timpul tastării
                let value = endDateInput.value.replace(/[^\d]/g, '');
                if (value.length >= 2) value = value.substring(0, 2) + '.' + value.substring(2);
                if (value.length >= 5) value = value.substring(0, 5) + '.' + value.substring(5, 9);
                endDateInput.value = value;
            });
        }

        /**
         * Inițializează funcționalitatea de filtrare a instanțelor după categorie
         */
        function initInstantaFiltering() {
            const categorieSelect = document.getElementById('categorieInstanta');
            const instantaSelect = document.getElementById('institutie');
            const warningElement = document.getElementById('institutionWarning');

            if (!categorieSelect || !instantaSelect) {
                return;
            }

            // Salvăm toate opțiunile originale
            const allOptions = Array.from(instantaSelect.options).slice(1); // Excludem prima opțiune "-- Toate instanțele --"

            // Coduri de instituții care pot avea probleme cu SOAP API
            const problematicCodes = [
                'InaltaCurtedeCASSATIESIJUSTITIE',
                'ICCJ',
                'TBBU',
                'CAB'
            ];

            // Funcție pentru afișarea avertismentului
            function showInstitutionWarning(show) {
                if (warningElement) {
                    warningElement.style.display = show ? 'block' : 'none';
                }
            }

            // Verificăm selecția instituției pentru avertismente
            instantaSelect.addEventListener('change', function() {
                const selectedValue = this.value;
                const isProblematic = problematicCodes.includes(selectedValue);
                showInstitutionWarning(isProblematic);
            });

            categorieSelect.addEventListener('change', function() {
                const selectedCategory = this.value;

                // Resetăm selectorul de instanțe
                instantaSelect.innerHTML = '<option value="">-- Toate instanțele --</option>';

                // Filtrăm opțiunile în funcție de categoria selectată
                let filteredOptions = allOptions;

                if (selectedCategory) {
                    filteredOptions = allOptions.filter(option => {
                        const value = option.value;
                        const text = option.textContent;

                        switch (selectedCategory) {
                            case 'curtea_suprema':
                                return value.includes('InaltaCurte') || text.includes('Înalta Curte');
                            case 'curte_apel':
                                return value.includes('CurteadeApel') || text.includes('Curtea de Apel');
                            case 'tribunal':
                                return value.includes('Tribunalul') || text.includes('Tribunalul');
                            case 'judecatorie':
                                return value.includes('Judecatoria') || text.includes('Judecătoria');
                            default:
                                return true;
                        }
                    });
                }

                // Adăugăm opțiunile filtrate
                filteredOptions.forEach(option => {
                    const newOption = option.cloneNode(true);
                    instantaSelect.appendChild(newOption);
                });

                // Dacă avem o valoare selectată anterior, o restaurăm
                const selectedInstitutie = '<?php echo htmlspecialchars($advancedFilters['institutie'] ?? ''); ?>';
                if (selectedInstitutie) {
                    instantaSelect.value = selectedInstitutie;
                    // Verificăm dacă trebuie să afișăm avertismentul
                    const isProblematic = problematicCodes.includes(selectedInstitutie);
                    showInstitutionWarning(isProblematic);
                }
            });

            // Inițializăm filtrul dacă avem o categorie selectată
            const selectedCategory = '<?php echo htmlspecialchars($advancedFilters['categorieInstanta'] ?? ''); ?>';
            if (selectedCategory) {
                categorieSelect.value = selectedCategory;
                categorieSelect.dispatchEvent(new Event('change'));
            }

            // Verificăm inițial dacă trebuie să afișăm avertismentul
            const initialInstitution = '<?php echo htmlspecialchars($advancedFilters['institutie'] ?? ''); ?>';
            if (initialInstitution && problematicCodes.includes(initialInstitution)) {
                showInstitutionWarning(true);
            }
        }

        /**
         * Initialize mobile search button functionality
         */
        function initMobileSearchButton() {
            const mobileSearchBtn = document.querySelector('.mobile-search-container .btn-primary');
            const originalForm = document.getElementById('bulkSearchForm');

            if (mobileSearchBtn && originalForm) {
                mobileSearchBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Add visual feedback
                    this.style.transform = 'translateY(-1px) scale(0.98)';

                    // Submit the form
                    originalForm.submit();

                    // Reset button state
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            }
        }

        /**
         * Handle URL parameters for party name search redirection
         */
        function handlePartyNameRedirection() {
            const urlParams = new URLSearchParams(window.location.search);
            const numeParte = urlParams.get('numeParte');
            const autoSubmit = urlParams.get('autoSubmit');

            if (numeParte) {
                // Populate the search field
                const searchField = document.getElementById('bulkSearchTerms');
                if (searchField && !searchField.value) {
                    searchField.value = decodeURIComponent(numeParte);

                    // Update term counter
                    if (typeof updateTermCounter === 'function') {
                        updateTermCounter();
                    }
                }

                // Show notification about the redirection
                if (typeof showNotification === 'function') {
                    showNotification(`Căutare automată pentru partea: "${decodeURIComponent(numeParte)}"`, 'info');
                }

                // If autoSubmit is true and we haven't already performed the search, submit the form
                if (autoSubmit === 'true') {
                    // Check if we already have search results (from PHP processing)
                    const hasResults = document.querySelector('.search-results-section');

                    if (!hasResults) {
                        // Submit the form automatically
                        const form = document.getElementById('bulkSearchForm');
                        if (form) {
                            setTimeout(() => {
                                form.submit();
                            }, 500); // Small delay to allow notification to show
                        }
                    }
                }

                // Clean up URL parameters to avoid confusion on page refresh
                if (window.history && window.history.replaceState) {
                    const cleanUrl = window.location.pathname;
                    window.history.replaceState({}, document.title, cleanUrl);
                }
            }
        }

        // Add to DOMContentLoaded event
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize functionality
            initTermCounter();
            initFormValidation();
            initNotificationSystem();
            initSortableTables();
            initInstantaFiltering(); // Add institution filtering
            initDateValidation(); // Add date validation
            initAdvancedFiltersToggle(); // Add advanced filters toggle
            initMobileSearchButton(); // Add mobile search button functionality

            // Handle party name redirection from detalii_dosar.php
            handlePartyNameRedirection();

            // Store current search parameters for export
            currentSearchTerms = document.getElementById('bulkSearchTerms').value;

            // Restore filter state
            setTimeout(restoreFilterState, 100); // Small delay to ensure DOM is ready
        });
    </script>
    </div> <!-- End content-wrapper -->

    <!-- Portal Information Section - Matching detalii_dosar.php styling -->
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-10">
                <!-- Compact Results Information -->
                <div class="results-info-compact mt-3 mb-2">
                    <div class="text-muted small">
                        <i class="fas fa-info-circle me-1" style="opacity: 0.6;"></i>
                        <span style="font-size: 0.8rem;">
                            Toate informațiile sunt preluate în timp real de la sistemul oficial al instanțelor de judecată.
                            Ultima actualizare: <?php echo date('d.m.Y H:i'); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer Section -->
    <footer class="modern-footer" role="contentinfo">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">
                        &copy; <?php echo date('Y'); ?> DosareJust.ro - Portal Judiciar. Toate drepturile rezervate.
                    </p>
                </div>
                <div class="col-md-6 text-md-right">
                    <div class="footer-links">
                        <a href="index.php" class="footer-link">Acasă</a>
                        <a href="http://portal.just.ro" target="_blank" class="footer-link">Portal Just</a>
                        <a href="#" class="footer-link">Contact</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTopBtn" class="back-to-top" title="Mergi sus" aria-label="Mergi la începutul paginii">
        <i class="fas fa-chevron-up" aria-hidden="true"></i>
    </button>

    <!-- Back to Top Script -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const backToTopBtn = document.getElementById('backToTopBtn');
        let isScrolling = false;

        // Throttled scroll handler for better performance
        function handleScroll() {
            if (!isScrolling) {
                window.requestAnimationFrame(function() {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                    if (scrollTop > 400) {
                        backToTopBtn.classList.add('visible');
                    } else {
                        backToTopBtn.classList.remove('visible');
                    }

                    isScrolling = false;
                });
                isScrolling = true;
            }
        }

        // Add scroll event listener
        window.addEventListener('scroll', handleScroll, { passive: true });

        // Enhanced click handler with smooth scroll
        backToTopBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Add active state
            this.style.transform = 'translateY(-1px) scale(0.95)';

            // Smooth scroll to top
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });

            // Reset button state
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // Focus management for accessibility
            document.body.focus();
        });

        // Keyboard accessibility
        backToTopBtn.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });

    // Romanian diacritics normalization function
    function normalizeRomanianText(text) {
        if (!text) return '';
        return text.toLowerCase()
            .replace(/ă/g, 'a')
            .replace(/â/g, 'a')
            .replace(/î/g, 'i')
            .replace(/ș/g, 's')
            .replace(/ț/g, 't');
    }

    // Initialize searchable institution dropdown
    function initSearchableInstitutionDropdown() {
        const searchInput = document.getElementById('institutieSearch');
        const hiddenSelect = document.getElementById('institutie');
        const dropdown = document.getElementById('institutieDropdown');

        if (!searchInput || !hiddenSelect || !dropdown) {
            console.log('Institution dropdown elements not found:', {
                searchInput: !!searchInput,
                hiddenSelect: !!hiddenSelect,
                dropdown: !!dropdown
            });
            return;
        }

        let options = [];
        let highlightedIndex = -1;

        // Build options array from select element
        Array.from(hiddenSelect.options).forEach(option => {
            if (option.value) {
                options.push({
                    value: option.value,
                    text: option.textContent.trim()
                });
            }
        });

        console.log('Institution dropdown initialized with', options.length, 'options');

        function renderDropdown(filteredOptions) {
            dropdown.innerHTML = '';

            if (filteredOptions.length === 0) {
                dropdown.innerHTML = '<div class="dropdown-item text-muted">Nu au fost găsite rezultate</div>';
                dropdown.style.display = 'block';
                return;
            }

            filteredOptions.forEach((option, index) => {
                const item = document.createElement('div');
                item.className = 'dropdown-item';
                item.textContent = option.text;
                item.dataset.value = option.value;
                item.dataset.index = index;

                item.addEventListener('click', function() {
                    selectOption(option);
                });

                dropdown.appendChild(item);
            });

            dropdown.style.display = 'block';
            highlightedIndex = -1;
        }

        function selectOption(option) {
            searchInput.value = option.text;
            hiddenSelect.value = option.value;
            dropdown.style.display = 'none';
            highlightedIndex = -1;
            console.log('Institution selected:', option);
        }

        function filterOptions(searchTerm) {
            const normalizedSearch = normalizeRomanianText(searchTerm);
            return options.filter(option =>
                normalizeRomanianText(option.text).includes(normalizedSearch)
            );
        }

        // Event listeners - Enhanced to show all options when empty
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim();
            if (searchTerm.length === 0) {
                // Show all options when input is empty
                renderDropdown(options);
                hiddenSelect.value = '';
                return;
            }

            const filteredOptions = filterOptions(searchTerm);
            renderDropdown(filteredOptions);
        });

        searchInput.addEventListener('focus', function() {
            const searchTerm = this.value.trim();
            if (searchTerm.length === 0) {
                // Show all options when input is empty and focused
                renderDropdown(options);
            } else {
                const filteredOptions = filterOptions(searchTerm);
                renderDropdown(filteredOptions);
            }
        });

        searchInput.addEventListener('click', function() {
            const searchTerm = this.value.trim();
            if (searchTerm.length === 0) {
                // Show all options when input is empty and clicked
                renderDropdown(options);
            } else {
                const filteredOptions = filterOptions(searchTerm);
                renderDropdown(filteredOptions);
            }
        });

        searchInput.addEventListener('keydown', function(e) {
            const items = dropdown.querySelectorAll('.dropdown-item:not(.text-muted)');

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                highlightedIndex = Math.min(highlightedIndex + 1, items.length - 1);
                updateHighlight(items);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                highlightedIndex = Math.max(highlightedIndex - 1, -1);
                updateHighlight(items);
            } else if (e.key === 'Enter') {
                e.preventDefault();
                if (highlightedIndex >= 0 && items[highlightedIndex]) {
                    const value = items[highlightedIndex].dataset.value;
                    const text = items[highlightedIndex].textContent;
                    selectOption({ value, text });
                }
            } else if (e.key === 'Escape') {
                dropdown.style.display = 'none';
                highlightedIndex = -1;
            }
        });

        function updateHighlight(items) {
            items.forEach((item, index) => {
                item.classList.toggle('highlighted', index === highlightedIndex);
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                dropdown.style.display = 'none';
                highlightedIndex = -1;
            }
        });
    }

    // Initialize searchable case category dropdown
    function initSearchableCaseCategoryDropdown() {
        const searchInput = document.getElementById('categorieCazSearch');
        const hiddenSelect = document.getElementById('categorieCaz');
        const dropdown = document.getElementById('categorieCazDropdown');

        if (!searchInput || !hiddenSelect || !dropdown) {
            console.log('Case category dropdown elements not found:', {
                searchInput: !!searchInput,
                hiddenSelect: !!hiddenSelect,
                dropdown: !!dropdown
            });
            return;
        }

        let options = [];
        let highlightedIndex = -1;

        // Build options array from select element
        Array.from(hiddenSelect.options).forEach(option => {
            if (option.value) {
                options.push({
                    value: option.value,
                    text: option.textContent.trim()
                });
            }
        });

        console.log('Case category dropdown initialized with', options.length, 'options');

        function renderDropdown(filteredOptions) {
            dropdown.innerHTML = '';

            if (filteredOptions.length === 0) {
                dropdown.innerHTML = '<div class="dropdown-item text-muted">Nu au fost găsite rezultate</div>';
                dropdown.style.display = 'block';
                return;
            }

            filteredOptions.forEach((option, index) => {
                const item = document.createElement('div');
                item.className = 'dropdown-item';
                item.textContent = option.text;
                item.dataset.value = option.value;
                item.dataset.index = index;

                item.addEventListener('click', function() {
                    selectOption(option);
                });

                dropdown.appendChild(item);
            });

            dropdown.style.display = 'block';
            highlightedIndex = -1;
        }

        function selectOption(option) {
            searchInput.value = option.text;
            hiddenSelect.value = option.value;
            dropdown.style.display = 'none';
            highlightedIndex = -1;
            console.log('Case category selected:', option);
        }

        function filterOptions(searchTerm) {
            const normalizedSearch = normalizeRomanianText(searchTerm);
            return options.filter(option =>
                normalizeRomanianText(option.text).includes(normalizedSearch)
            );
        }

        // Event listeners - Enhanced to show all options when empty
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim();
            if (searchTerm.length === 0) {
                // Show all options when input is empty
                renderDropdown(options);
                hiddenSelect.value = '';
                return;
            }

            const filteredOptions = filterOptions(searchTerm);
            renderDropdown(filteredOptions);
        });

        searchInput.addEventListener('focus', function() {
            const searchTerm = this.value.trim();
            if (searchTerm.length === 0) {
                // Show all options when input is empty and focused
                renderDropdown(options);
            } else {
                const filteredOptions = filterOptions(searchTerm);
                renderDropdown(filteredOptions);
            }
        });

        searchInput.addEventListener('click', function() {
            const searchTerm = this.value.trim();
            if (searchTerm.length === 0) {
                // Show all options when input is empty and clicked
                renderDropdown(options);
            } else {
                const filteredOptions = filterOptions(searchTerm);
                renderDropdown(filteredOptions);
            }
        });

        searchInput.addEventListener('keydown', function(e) {
            const items = dropdown.querySelectorAll('.dropdown-item:not(.text-muted)');

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                highlightedIndex = Math.min(highlightedIndex + 1, items.length - 1);
                updateHighlight(items);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                highlightedIndex = Math.max(highlightedIndex - 1, -1);
                updateHighlight(items);
            } else if (e.key === 'Enter') {
                e.preventDefault();
                if (highlightedIndex >= 0 && items[highlightedIndex]) {
                    const value = items[highlightedIndex].dataset.value;
                    const text = items[highlightedIndex].textContent;
                    selectOption({ value, text });
                }
            } else if (e.key === 'Escape') {
                dropdown.style.display = 'none';
                highlightedIndex = -1;
            }
        });

        function updateHighlight(items) {
            items.forEach((item, index) => {
                item.classList.toggle('highlighted', index === highlightedIndex);
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                dropdown.style.display = 'none';
                highlightedIndex = -1;
            }
        });
    }

    // Initialize dropdowns when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Initializing searchable dropdowns...');
        initSearchableInstitutionDropdown();
        initSearchableCaseCategoryDropdown();
    });
    </script>
</body>
</html>
